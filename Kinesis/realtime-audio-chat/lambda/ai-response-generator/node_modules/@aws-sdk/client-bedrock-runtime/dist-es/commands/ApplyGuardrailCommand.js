import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ApplyGuardrailRequestFilterSensitiveLog, ApplyGuardrailResponseFilterSensitiveLog, } from "../models/models_0";
import { de_ApplyGuardrailCommand, se_ApplyGuardrailCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ApplyGuardrailCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockFrontendService", "ApplyGuardrail", {})
    .n("BedrockRuntimeClient", "ApplyGuardrailCommand")
    .f(ApplyGuardrailRequestFilterSensitiveLog, ApplyGuardrailResponseFilterSensitiveLog)
    .ser(se_ApplyGuardrailCommand)
    .de(de_ApplyGuardrailCommand)
    .build() {
}
