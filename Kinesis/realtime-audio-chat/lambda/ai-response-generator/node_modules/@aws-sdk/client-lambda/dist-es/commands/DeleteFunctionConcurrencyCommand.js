import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_DeleteFunctionConcurrencyCommand, se_DeleteFunctionConcurrencyCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class DeleteFunctionConcurrencyCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "DeleteFunctionConcurrency", {})
    .n("LambdaClient", "DeleteFunctionConcurrencyCommand")
    .f(void 0, void 0)
    .ser(se_DeleteFunctionConcurrencyCommand)
    .de(de_DeleteFunctionConcurrencyCommand)
    .build() {
}
