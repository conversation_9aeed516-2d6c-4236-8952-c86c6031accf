import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as apigatewayv2 from 'aws-cdk-lib/aws-apigatewayv2';
import * as apigatewayv2Integrations from 'aws-cdk-lib/aws-apigatewayv2-integrations';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import { Construct } from 'constructs';

export class RealtimeAudioChatStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // DynamoDB Tables
    const conversationSessionsTable = new dynamodb.Table(this, 'ConversationSessions', {
      tableName: 'realtime-audio-chat-sessions',
      partitionKey: { name: 'sessionId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'userId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
      pointInTimeRecovery: true,
      stream: dynamodb.StreamViewType.NEW_AND_OLD_IMAGES,
    });

    const messagesTable = new dynamodb.Table(this, 'Messages', {
      tableName: 'realtime-audio-chat-messages',
      partitionKey: { name: 'sessionId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'messageId', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
      pointInTimeRecovery: true,
    });

    // Add GSI for timestamp-based queries
    messagesTable.addGlobalSecondaryIndex({
      indexName: 'TimestampIndex',
      partitionKey: { name: 'sessionId', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'timestamp', type: dynamodb.AttributeType.NUMBER },
    });

    // IAM Role for Lambda functions
    const lambdaExecutionRole = new iam.Role(this, 'LambdaExecutionRole', {
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
      ],
      inlinePolicies: {
        DynamoDBAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'dynamodb:GetItem',
                'dynamodb:PutItem',
                'dynamodb:UpdateItem',
                'dynamodb:DeleteItem',
                'dynamodb:Query',
                'dynamodb:Scan',
              ],
              resources: [
                conversationSessionsTable.tableArn,
                messagesTable.tableArn,
                `${messagesTable.tableArn}/index/*`,
              ],
            }),
          ],
        }),
        KinesisVideoAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'kinesisvideo:GetDataEndpoint',
                'kinesisvideo:GetMedia',
                'kinesisvideo:ListFragments',
                'kinesisvideo:DescribeStream',
              ],
              resources: ['arn:aws:kinesisvideo:us-east-1:122610497322:stream/my-webrtc-channel/*'],
            }),
          ],
        }),
        TranscribeAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'transcribe:StartStreamTranscription',
                'transcribe:StartStreamTranscriptionWebSocket',
              ],
              resources: ['*'],
            }),
          ],
        }),
        BedrockAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'bedrock:InvokeModel',
                'bedrock:InvokeModelWithResponseStream',
              ],
              resources: ['arn:aws:bedrock:*:*:foundation-model/anthropic.claude-*'],
            }),
          ],
        }),
        PollyAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'polly:SynthesizeSpeech',
                'polly:StartSpeechSynthesisTask',
              ],
              resources: ['*'],
            }),
          ],
        }),
        ApiGatewayAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'execute-api:ManageConnections',
              ],
              resources: ['*'],
            }),
          ],
        }),
        LambdaInvokeAccess: new iam.PolicyDocument({
          statements: [
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'lambda:InvokeFunction',
              ],
              resources: ['arn:aws:lambda:*:*:function:realtime-audio-chat-*'],
            }),
          ],
        }),
      },
    });

    // Lambda Functions
    const streamProcessorFunction = new lambda.Function(this, 'StreamProcessor', {
      functionName: 'realtime-audio-chat-stream-processor',
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../lambda/stream-processor', {
        bundling: {
          image: lambda.Runtime.NODEJS_18_X.bundlingImage,
          command: [
            'bash', '-c',
            'npm ci && npm run build && cp -r dist/* /asset-output/ && cp package.json /asset-output/'
          ],
        },
      }),
      role: lambdaExecutionRole,
      timeout: cdk.Duration.minutes(5),
      environment: {
        SESSIONS_TABLE: conversationSessionsTable.tableName,
        MESSAGES_TABLE: messagesTable.tableName,
        STREAM_ARN: 'arn:aws:kinesisvideo:us-east-1:122610497322:stream/my-webrtc-channel/1755712113724',
        TRANSCRIPTION_HANDLER_FUNCTION: 'realtime-audio-chat-transcription-handler',
      },
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    const transcriptionHandlerFunction = new lambda.Function(this, 'TranscriptionHandler', {
      functionName: 'realtime-audio-chat-transcription-handler',
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../lambda/transcription-handler', {
        bundling: {
          image: lambda.Runtime.NODEJS_18_X.bundlingImage,
          command: [
            'bash', '-c',
            'npm ci && npm run build && cp -r dist/* /asset-output/ && cp package.json /asset-output/'
          ],
        },
      }),
      role: lambdaExecutionRole,
      timeout: cdk.Duration.minutes(5),
      environment: {
        SESSIONS_TABLE: conversationSessionsTable.tableName,
        MESSAGES_TABLE: messagesTable.tableName,
        AI_RESPONSE_GENERATOR_FUNCTION: 'realtime-audio-chat-ai-response-generator',
        WEBSOCKET_API_ENDPOINT: '', // Will be set after WebSocket API creation
      },
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    const aiResponseGeneratorFunction = new lambda.Function(this, 'AIResponseGenerator', {
      functionName: 'realtime-audio-chat-ai-response-generator',
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../lambda/ai-response-generator', {
        bundling: {
          image: lambda.Runtime.NODEJS_18_X.bundlingImage,
          command: [
            'bash', '-c',
            'npm ci && npm run build && cp -r dist/* /asset-output/ && cp package.json /asset-output/'
          ],
        },
      }),
      role: lambdaExecutionRole,
      timeout: cdk.Duration.minutes(5),
      environment: {
        CONVERSATIONS_TABLE: conversationSessionsTable.tableName,
        WEBSOCKET_API_ENDPOINT: '', // Will be set after WebSocket API creation
        BEDROCK_MODEL_ID: 'anthropic.claude-3-sonnet-20240229-v1:0',
        AUDIO_SYNTHESIZER_FUNCTION: 'realtime-audio-chat-audio-synthesizer',
      },
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    const audioSynthesizerFunction = new lambda.Function(this, 'AudioSynthesizer', {
      functionName: 'realtime-audio-chat-audio-synthesizer',
      runtime: lambda.Runtime.NODEJS_18_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('../lambda/audio-synthesizer', {
        bundling: {
          image: lambda.Runtime.NODEJS_18_X.bundlingImage,
          command: [
            'bash', '-c',
            'npm ci && npm run build && cp -r dist/* /asset-output/ && cp package.json /asset-output/'
          ],
        },
      }),
      role: lambdaExecutionRole,
      timeout: cdk.Duration.minutes(5),
      environment: {
        CONVERSATIONS_TABLE: conversationSessionsTable.tableName,
        WEBSOCKET_API_ENDPOINT: '', // Will be set after WebSocket API creation
      },
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    // WebSocket API Gateway
    const webSocketApi = new apigatewayv2.WebSocketApi(this, 'RealtimeAudioChatWebSocketApi', {
      apiName: 'realtime-audio-chat-websocket',
      description: 'WebSocket API for real-time audio chat communication',
    });

    // WebSocket Lambda integrations
    const connectIntegration = new apigatewayv2Integrations.WebSocketLambdaIntegration(
      'ConnectIntegration',
      streamProcessorFunction
    );

    const disconnectIntegration = new apigatewayv2Integrations.WebSocketLambdaIntegration(
      'DisconnectIntegration',
      streamProcessorFunction
    );

    const defaultIntegration = new apigatewayv2Integrations.WebSocketLambdaIntegration(
      'DefaultIntegration',
      streamProcessorFunction
    );

    // WebSocket routes
    webSocketApi.addRoute('$connect', {
      integration: connectIntegration,
    });

    webSocketApi.addRoute('$disconnect', {
      integration: disconnectIntegration,
    });

    webSocketApi.addRoute('$default', {
      integration: defaultIntegration,
    });

    // WebSocket stage
    const webSocketStage = new apigatewayv2.WebSocketStage(this, 'RealtimeAudioChatWebSocketStage', {
      webSocketApi,
      stageName: 'prod',
      autoDeploy: true,
    });

    // Grant Lambda functions permission to manage WebSocket connections
    webSocketApi.grantManageConnections(streamProcessorFunction);
    webSocketApi.grantManageConnections(transcriptionHandlerFunction);
    webSocketApi.grantManageConnections(aiResponseGeneratorFunction);
    webSocketApi.grantManageConnections(audioSynthesizerFunction);

    // Update functions with WebSocket API endpoint
    transcriptionHandlerFunction.addEnvironment('WEBSOCKET_API_ENDPOINT', webSocketStage.url);
    aiResponseGeneratorFunction.addEnvironment('WEBSOCKET_API_ENDPOINT', webSocketStage.url);
    audioSynthesizerFunction.addEnvironment('WEBSOCKET_API_ENDPOINT', webSocketStage.url);

    // EventBridge rule for Kinesis Video Stream events
    // Note: In production, this would be configured to trigger on actual stream events
    const streamProcessingRule = new events.Rule(this, 'StreamProcessingRule', {
      ruleName: 'realtime-audio-chat-stream-processing',
      description: 'Triggers stream processor when Kinesis Video Stream receives data',
      // This is a placeholder - actual implementation would use Kinesis Video Stream events
      eventPattern: {
        source: ['aws.kinesisvideo'],
        detailType: ['Kinesis Video Stream Fragment'],
      },
      enabled: true,
    });

    // Add Lambda target to the rule
    streamProcessingRule.addTarget(new targets.LambdaFunction(streamProcessorFunction, {
      retryAttempts: 3,
    }));

    // Outputs
    new cdk.CfnOutput(this, 'WebSocketApiEndpoint', {
      value: webSocketStage.url,
      description: 'WebSocket API endpoint URL',
    });

    new cdk.CfnOutput(this, 'ConversationSessionsTableName', {
      value: conversationSessionsTable.tableName,
      description: 'DynamoDB table name for conversation sessions',
    });

    new cdk.CfnOutput(this, 'MessagesTableName', {
      value: messagesTable.tableName,
      description: 'DynamoDB table name for messages',
    });

    new cdk.CfnOutput(this, 'StreamProcessorFunctionName', {
      value: streamProcessorFunction.functionName,
      description: 'Stream processor Lambda function name',
    });

    new cdk.CfnOutput(this, 'TranscriptionHandlerFunctionName', {
      value: transcriptionHandlerFunction.functionName,
      description: 'Transcription handler Lambda function name',
    });

    new cdk.CfnOutput(this, 'AIResponseGeneratorFunctionName', {
      value: aiResponseGeneratorFunction.functionName,
      description: 'AI response generator Lambda function name',
    });

    new cdk.CfnOutput(this, 'AudioSynthesizerFunctionName', {
      value: audioSynthesizerFunction.functionName,
      description: 'Audio synthesizer Lambda function name',
    });
  }
}