import { Paginator } from "@smithy/types";
import {
  ListAsyncInvokesCommandInput,
  ListAsyncInvokesCommandOutput,
} from "../commands/ListAsyncInvokesCommand";
import { BedrockRuntimePaginationConfiguration } from "./Interfaces";
export declare const paginateListAsyncInvokes: (
  config: BedrockRuntimePaginationConfiguration,
  input: ListAsyncInvokesCommandInput,
  ...rest: any[]
) => Paginator<ListAsyncInvokesCommandOutput>;
