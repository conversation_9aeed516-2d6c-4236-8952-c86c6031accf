import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ApiGatewayManagementApiClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ApiGatewayManagementApiClient";
import {
  GetConnectionRequest,
  GetConnectionResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface GetConnectionCommandInput extends GetConnectionRequest {}
export interface GetConnectionCommandOutput
  extends GetConnectionResponse,
    __MetadataBearer {}
declare const GetConnectionCommand_base: {
  new (
    input: GetConnectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetConnectionCommandInput,
    GetConnectionCommandOutput,
    ApiGatewayManagementApiClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetConnectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetConnectionCommandInput,
    GetConnectionCommandOutput,
    ApiGatewayManagementApiClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetConnectionCommand extends GetConnectionCommand_base {
  protected static __types: {
    api: {
      input: GetConnectionRequest;
      output: GetConnectionResponse;
    };
    sdk: {
      input: GetConnectionCommandInput;
      output: GetConnectionCommandOutput;
    };
  };
}
