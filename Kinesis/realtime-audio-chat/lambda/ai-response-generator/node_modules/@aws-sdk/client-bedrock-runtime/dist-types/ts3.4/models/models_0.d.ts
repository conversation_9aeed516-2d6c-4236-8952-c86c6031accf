import { ExceptionOptionType as __ExceptionOptionType } from "@smithy/smithy-client";
import { DocumentType as __DocumentType } from "@smithy/types";
import { BedrockRuntimeServiceException as __BaseException } from "./BedrockRuntimeServiceException";
export declare class AccessDeniedException extends __BaseException {
  readonly name: "AccessDeniedException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<AccessDeniedException, __BaseException>
  );
}
export interface GetAsyncInvokeRequest {
  invocationArn: string | undefined;
}
export interface AsyncInvokeS3OutputDataConfig {
  s3Uri: string | undefined;
  kmsKeyId?: string | undefined;
  bucketOwner?: string | undefined;
}
export type AsyncInvokeOutputDataConfig =
  | AsyncInvokeOutputDataConfig.S3OutputDataConfigMember
  | AsyncInvokeOutputDataConfig.$UnknownMember;
export declare namespace AsyncInvokeOutputDataConfig {
  interface S3OutputDataConfigMember {
    s3OutputDataConfig: AsyncInvokeS3OutputDataConfig;
    $unknown?: never;
  }
  interface $UnknownMember {
    s3OutputDataConfig?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    s3OutputDataConfig: (value: AsyncInvokeS3OutputDataConfig) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: AsyncInvokeOutputDataConfig,
    visitor: Visitor<T>
  ) => T;
}
export declare const AsyncInvokeStatus: {
  readonly COMPLETED: "Completed";
  readonly FAILED: "Failed";
  readonly IN_PROGRESS: "InProgress";
};
export type AsyncInvokeStatus =
  (typeof AsyncInvokeStatus)[keyof typeof AsyncInvokeStatus];
export interface GetAsyncInvokeResponse {
  invocationArn: string | undefined;
  modelArn: string | undefined;
  clientRequestToken?: string | undefined;
  status: AsyncInvokeStatus | undefined;
  failureMessage?: string | undefined;
  submitTime: Date | undefined;
  lastModifiedTime?: Date | undefined;
  endTime?: Date | undefined;
  outputDataConfig: AsyncInvokeOutputDataConfig | undefined;
}
export declare class InternalServerException extends __BaseException {
  readonly name: "InternalServerException";
  readonly $fault: "server";
  constructor(
    opts: __ExceptionOptionType<InternalServerException, __BaseException>
  );
}
export declare class ThrottlingException extends __BaseException {
  readonly name: "ThrottlingException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ThrottlingException, __BaseException>
  );
}
export declare class ValidationException extends __BaseException {
  readonly name: "ValidationException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ValidationException, __BaseException>
  );
}
export declare const SortAsyncInvocationBy: {
  readonly SUBMISSION_TIME: "SubmissionTime";
};
export type SortAsyncInvocationBy =
  (typeof SortAsyncInvocationBy)[keyof typeof SortAsyncInvocationBy];
export declare const SortOrder: {
  readonly ASCENDING: "Ascending";
  readonly DESCENDING: "Descending";
};
export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder];
export interface ListAsyncInvokesRequest {
  submitTimeAfter?: Date | undefined;
  submitTimeBefore?: Date | undefined;
  statusEquals?: AsyncInvokeStatus | undefined;
  maxResults?: number | undefined;
  nextToken?: string | undefined;
  sortBy?: SortAsyncInvocationBy | undefined;
  sortOrder?: SortOrder | undefined;
}
export interface AsyncInvokeSummary {
  invocationArn: string | undefined;
  modelArn: string | undefined;
  clientRequestToken?: string | undefined;
  status?: AsyncInvokeStatus | undefined;
  failureMessage?: string | undefined;
  submitTime: Date | undefined;
  lastModifiedTime?: Date | undefined;
  endTime?: Date | undefined;
  outputDataConfig: AsyncInvokeOutputDataConfig | undefined;
}
export interface ListAsyncInvokesResponse {
  nextToken?: string | undefined;
  asyncInvokeSummaries?: AsyncInvokeSummary[] | undefined;
}
export declare class ConflictException extends __BaseException {
  readonly name: "ConflictException";
  readonly $fault: "client";
  constructor(opts: __ExceptionOptionType<ConflictException, __BaseException>);
}
export declare class ResourceNotFoundException extends __BaseException {
  readonly name: "ResourceNotFoundException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ResourceNotFoundException, __BaseException>
  );
}
export declare class ServiceQuotaExceededException extends __BaseException {
  readonly name: "ServiceQuotaExceededException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ServiceQuotaExceededException, __BaseException>
  );
}
export declare class ServiceUnavailableException extends __BaseException {
  readonly name: "ServiceUnavailableException";
  readonly $fault: "server";
  constructor(
    opts: __ExceptionOptionType<ServiceUnavailableException, __BaseException>
  );
}
export interface Tag {
  key: string | undefined;
  value: string | undefined;
}
export interface StartAsyncInvokeRequest {
  clientRequestToken?: string | undefined;
  modelId: string | undefined;
  modelInput: __DocumentType | undefined;
  outputDataConfig: AsyncInvokeOutputDataConfig | undefined;
  tags?: Tag[] | undefined;
}
export interface StartAsyncInvokeResponse {
  invocationArn: string | undefined;
}
export declare const GuardrailImageFormat: {
  readonly JPEG: "jpeg";
  readonly PNG: "png";
};
export type GuardrailImageFormat =
  (typeof GuardrailImageFormat)[keyof typeof GuardrailImageFormat];
export type GuardrailImageSource =
  | GuardrailImageSource.BytesMember
  | GuardrailImageSource.$UnknownMember;
export declare namespace GuardrailImageSource {
  interface BytesMember {
    bytes: Uint8Array;
    $unknown?: never;
  }
  interface $UnknownMember {
    bytes?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    bytes: (value: Uint8Array) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: GuardrailImageSource, visitor: Visitor<T>) => T;
}
export interface GuardrailImageBlock {
  format: GuardrailImageFormat | undefined;
  source: GuardrailImageSource | undefined;
}
export declare const GuardrailContentQualifier: {
  readonly GROUNDING_SOURCE: "grounding_source";
  readonly GUARD_CONTENT: "guard_content";
  readonly QUERY: "query";
};
export type GuardrailContentQualifier =
  (typeof GuardrailContentQualifier)[keyof typeof GuardrailContentQualifier];
export interface GuardrailTextBlock {
  text: string | undefined;
  qualifiers?: GuardrailContentQualifier[] | undefined;
}
export type GuardrailContentBlock =
  | GuardrailContentBlock.ImageMember
  | GuardrailContentBlock.TextMember
  | GuardrailContentBlock.$UnknownMember;
export declare namespace GuardrailContentBlock {
  interface TextMember {
    text: GuardrailTextBlock;
    image?: never;
    $unknown?: never;
  }
  interface ImageMember {
    text?: never;
    image: GuardrailImageBlock;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    image?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: GuardrailTextBlock) => T;
    image: (value: GuardrailImageBlock) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: GuardrailContentBlock, visitor: Visitor<T>) => T;
}
export declare const GuardrailOutputScope: {
  readonly FULL: "FULL";
  readonly INTERVENTIONS: "INTERVENTIONS";
};
export type GuardrailOutputScope =
  (typeof GuardrailOutputScope)[keyof typeof GuardrailOutputScope];
export declare const GuardrailContentSource: {
  readonly INPUT: "INPUT";
  readonly OUTPUT: "OUTPUT";
};
export type GuardrailContentSource =
  (typeof GuardrailContentSource)[keyof typeof GuardrailContentSource];
export interface ApplyGuardrailRequest {
  guardrailIdentifier: string | undefined;
  guardrailVersion: string | undefined;
  source: GuardrailContentSource | undefined;
  content: GuardrailContentBlock[] | undefined;
  outputScope?: GuardrailOutputScope | undefined;
}
export declare const GuardrailAction: {
  readonly GUARDRAIL_INTERVENED: "GUARDRAIL_INTERVENED";
  readonly NONE: "NONE";
};
export type GuardrailAction =
  (typeof GuardrailAction)[keyof typeof GuardrailAction];
export interface GuardrailAutomatedReasoningRule {
  identifier?: string | undefined;
  policyVersionArn?: string | undefined;
}
export interface GuardrailAutomatedReasoningStatement {
  logic?: string | undefined;
  naturalLanguage?: string | undefined;
}
export declare const GuardrailAutomatedReasoningLogicWarningType: {
  readonly ALWAYS_FALSE: "ALWAYS_FALSE";
  readonly ALWAYS_TRUE: "ALWAYS_TRUE";
};
export type GuardrailAutomatedReasoningLogicWarningType =
  (typeof GuardrailAutomatedReasoningLogicWarningType)[keyof typeof GuardrailAutomatedReasoningLogicWarningType];
export interface GuardrailAutomatedReasoningLogicWarning {
  type?: GuardrailAutomatedReasoningLogicWarningType | undefined;
  premises?: GuardrailAutomatedReasoningStatement[] | undefined;
  claims?: GuardrailAutomatedReasoningStatement[] | undefined;
}
export interface GuardrailAutomatedReasoningInputTextReference {
  text?: string | undefined;
}
export interface GuardrailAutomatedReasoningTranslation {
  premises?: GuardrailAutomatedReasoningStatement[] | undefined;
  claims?: GuardrailAutomatedReasoningStatement[] | undefined;
  untranslatedPremises?:
    | GuardrailAutomatedReasoningInputTextReference[]
    | undefined;
  untranslatedClaims?:
    | GuardrailAutomatedReasoningInputTextReference[]
    | undefined;
  confidence?: number | undefined;
}
export interface GuardrailAutomatedReasoningImpossibleFinding {
  translation?: GuardrailAutomatedReasoningTranslation | undefined;
  contradictingRules?: GuardrailAutomatedReasoningRule[] | undefined;
  logicWarning?: GuardrailAutomatedReasoningLogicWarning | undefined;
}
export interface GuardrailAutomatedReasoningInvalidFinding {
  translation?: GuardrailAutomatedReasoningTranslation | undefined;
  contradictingRules?: GuardrailAutomatedReasoningRule[] | undefined;
  logicWarning?: GuardrailAutomatedReasoningLogicWarning | undefined;
}
export interface GuardrailAutomatedReasoningNoTranslationsFinding {}
export interface GuardrailAutomatedReasoningScenario {
  statements?: GuardrailAutomatedReasoningStatement[] | undefined;
}
export interface GuardrailAutomatedReasoningSatisfiableFinding {
  translation?: GuardrailAutomatedReasoningTranslation | undefined;
  claimsTrueScenario?: GuardrailAutomatedReasoningScenario | undefined;
  claimsFalseScenario?: GuardrailAutomatedReasoningScenario | undefined;
  logicWarning?: GuardrailAutomatedReasoningLogicWarning | undefined;
}
export interface GuardrailAutomatedReasoningTooComplexFinding {}
export interface GuardrailAutomatedReasoningTranslationOption {
  translations?: GuardrailAutomatedReasoningTranslation[] | undefined;
}
export interface GuardrailAutomatedReasoningTranslationAmbiguousFinding {
  options?: GuardrailAutomatedReasoningTranslationOption[] | undefined;
  differenceScenarios?: GuardrailAutomatedReasoningScenario[] | undefined;
}
export interface GuardrailAutomatedReasoningValidFinding {
  translation?: GuardrailAutomatedReasoningTranslation | undefined;
  claimsTrueScenario?: GuardrailAutomatedReasoningScenario | undefined;
  supportingRules?: GuardrailAutomatedReasoningRule[] | undefined;
  logicWarning?: GuardrailAutomatedReasoningLogicWarning | undefined;
}
export type GuardrailAutomatedReasoningFinding =
  | GuardrailAutomatedReasoningFinding.ImpossibleMember
  | GuardrailAutomatedReasoningFinding.InvalidMember
  | GuardrailAutomatedReasoningFinding.NoTranslationsMember
  | GuardrailAutomatedReasoningFinding.SatisfiableMember
  | GuardrailAutomatedReasoningFinding.TooComplexMember
  | GuardrailAutomatedReasoningFinding.TranslationAmbiguousMember
  | GuardrailAutomatedReasoningFinding.ValidMember
  | GuardrailAutomatedReasoningFinding.$UnknownMember;
export declare namespace GuardrailAutomatedReasoningFinding {
  interface ValidMember {
    valid: GuardrailAutomatedReasoningValidFinding;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface InvalidMember {
    valid?: never;
    invalid: GuardrailAutomatedReasoningInvalidFinding;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface SatisfiableMember {
    valid?: never;
    invalid?: never;
    satisfiable: GuardrailAutomatedReasoningSatisfiableFinding;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface ImpossibleMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible: GuardrailAutomatedReasoningImpossibleFinding;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface TranslationAmbiguousMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous: GuardrailAutomatedReasoningTranslationAmbiguousFinding;
    tooComplex?: never;
    noTranslations?: never;
    $unknown?: never;
  }
  interface TooComplexMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex: GuardrailAutomatedReasoningTooComplexFinding;
    noTranslations?: never;
    $unknown?: never;
  }
  interface NoTranslationsMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations: GuardrailAutomatedReasoningNoTranslationsFinding;
    $unknown?: never;
  }
  interface $UnknownMember {
    valid?: never;
    invalid?: never;
    satisfiable?: never;
    impossible?: never;
    translationAmbiguous?: never;
    tooComplex?: never;
    noTranslations?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    valid: (value: GuardrailAutomatedReasoningValidFinding) => T;
    invalid: (value: GuardrailAutomatedReasoningInvalidFinding) => T;
    satisfiable: (value: GuardrailAutomatedReasoningSatisfiableFinding) => T;
    impossible: (value: GuardrailAutomatedReasoningImpossibleFinding) => T;
    translationAmbiguous: (
      value: GuardrailAutomatedReasoningTranslationAmbiguousFinding
    ) => T;
    tooComplex: (value: GuardrailAutomatedReasoningTooComplexFinding) => T;
    noTranslations: (
      value: GuardrailAutomatedReasoningNoTranslationsFinding
    ) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: GuardrailAutomatedReasoningFinding,
    visitor: Visitor<T>
  ) => T;
}
export interface GuardrailAutomatedReasoningPolicyAssessment {
  findings?: GuardrailAutomatedReasoningFinding[] | undefined;
}
export declare const GuardrailContentPolicyAction: {
  readonly BLOCKED: "BLOCKED";
  readonly NONE: "NONE";
};
export type GuardrailContentPolicyAction =
  (typeof GuardrailContentPolicyAction)[keyof typeof GuardrailContentPolicyAction];
export declare const GuardrailContentFilterConfidence: {
  readonly HIGH: "HIGH";
  readonly LOW: "LOW";
  readonly MEDIUM: "MEDIUM";
  readonly NONE: "NONE";
};
export type GuardrailContentFilterConfidence =
  (typeof GuardrailContentFilterConfidence)[keyof typeof GuardrailContentFilterConfidence];
export declare const GuardrailContentFilterStrength: {
  readonly HIGH: "HIGH";
  readonly LOW: "LOW";
  readonly MEDIUM: "MEDIUM";
  readonly NONE: "NONE";
};
export type GuardrailContentFilterStrength =
  (typeof GuardrailContentFilterStrength)[keyof typeof GuardrailContentFilterStrength];
export declare const GuardrailContentFilterType: {
  readonly HATE: "HATE";
  readonly INSULTS: "INSULTS";
  readonly MISCONDUCT: "MISCONDUCT";
  readonly PROMPT_ATTACK: "PROMPT_ATTACK";
  readonly SEXUAL: "SEXUAL";
  readonly VIOLENCE: "VIOLENCE";
};
export type GuardrailContentFilterType =
  (typeof GuardrailContentFilterType)[keyof typeof GuardrailContentFilterType];
export interface GuardrailContentFilter {
  type: GuardrailContentFilterType | undefined;
  confidence: GuardrailContentFilterConfidence | undefined;
  filterStrength?: GuardrailContentFilterStrength | undefined;
  action: GuardrailContentPolicyAction | undefined;
  detected?: boolean | undefined;
}
export interface GuardrailContentPolicyAssessment {
  filters: GuardrailContentFilter[] | undefined;
}
export declare const GuardrailContextualGroundingPolicyAction: {
  readonly BLOCKED: "BLOCKED";
  readonly NONE: "NONE";
};
export type GuardrailContextualGroundingPolicyAction =
  (typeof GuardrailContextualGroundingPolicyAction)[keyof typeof GuardrailContextualGroundingPolicyAction];
export declare const GuardrailContextualGroundingFilterType: {
  readonly GROUNDING: "GROUNDING";
  readonly RELEVANCE: "RELEVANCE";
};
export type GuardrailContextualGroundingFilterType =
  (typeof GuardrailContextualGroundingFilterType)[keyof typeof GuardrailContextualGroundingFilterType];
export interface GuardrailContextualGroundingFilter {
  type: GuardrailContextualGroundingFilterType | undefined;
  threshold: number | undefined;
  score: number | undefined;
  action: GuardrailContextualGroundingPolicyAction | undefined;
  detected?: boolean | undefined;
}
export interface GuardrailContextualGroundingPolicyAssessment {
  filters?: GuardrailContextualGroundingFilter[] | undefined;
}
export interface GuardrailImageCoverage {
  guarded?: number | undefined;
  total?: number | undefined;
}
export interface GuardrailTextCharactersCoverage {
  guarded?: number | undefined;
  total?: number | undefined;
}
export interface GuardrailCoverage {
  textCharacters?: GuardrailTextCharactersCoverage | undefined;
  images?: GuardrailImageCoverage | undefined;
}
export interface GuardrailUsage {
  topicPolicyUnits: number | undefined;
  contentPolicyUnits: number | undefined;
  wordPolicyUnits: number | undefined;
  sensitiveInformationPolicyUnits: number | undefined;
  sensitiveInformationPolicyFreeUnits: number | undefined;
  contextualGroundingPolicyUnits: number | undefined;
  contentPolicyImageUnits?: number | undefined;
  automatedReasoningPolicyUnits?: number | undefined;
  automatedReasoningPolicies?: number | undefined;
}
export interface GuardrailInvocationMetrics {
  guardrailProcessingLatency?: number | undefined;
  usage?: GuardrailUsage | undefined;
  guardrailCoverage?: GuardrailCoverage | undefined;
}
export declare const GuardrailSensitiveInformationPolicyAction: {
  readonly ANONYMIZED: "ANONYMIZED";
  readonly BLOCKED: "BLOCKED";
  readonly NONE: "NONE";
};
export type GuardrailSensitiveInformationPolicyAction =
  (typeof GuardrailSensitiveInformationPolicyAction)[keyof typeof GuardrailSensitiveInformationPolicyAction];
export declare const GuardrailPiiEntityType: {
  readonly ADDRESS: "ADDRESS";
  readonly AGE: "AGE";
  readonly AWS_ACCESS_KEY: "AWS_ACCESS_KEY";
  readonly AWS_SECRET_KEY: "AWS_SECRET_KEY";
  readonly CA_HEALTH_NUMBER: "CA_HEALTH_NUMBER";
  readonly CA_SOCIAL_INSURANCE_NUMBER: "CA_SOCIAL_INSURANCE_NUMBER";
  readonly CREDIT_DEBIT_CARD_CVV: "CREDIT_DEBIT_CARD_CVV";
  readonly CREDIT_DEBIT_CARD_EXPIRY: "CREDIT_DEBIT_CARD_EXPIRY";
  readonly CREDIT_DEBIT_CARD_NUMBER: "CREDIT_DEBIT_CARD_NUMBER";
  readonly DRIVER_ID: "DRIVER_ID";
  readonly EMAIL: "EMAIL";
  readonly INTERNATIONAL_BANK_ACCOUNT_NUMBER: "INTERNATIONAL_BANK_ACCOUNT_NUMBER";
  readonly IP_ADDRESS: "IP_ADDRESS";
  readonly LICENSE_PLATE: "LICENSE_PLATE";
  readonly MAC_ADDRESS: "MAC_ADDRESS";
  readonly NAME: "NAME";
  readonly PASSWORD: "PASSWORD";
  readonly PHONE: "PHONE";
  readonly PIN: "PIN";
  readonly SWIFT_CODE: "SWIFT_CODE";
  readonly UK_NATIONAL_HEALTH_SERVICE_NUMBER: "UK_NATIONAL_HEALTH_SERVICE_NUMBER";
  readonly UK_NATIONAL_INSURANCE_NUMBER: "UK_NATIONAL_INSURANCE_NUMBER";
  readonly UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER: "UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER";
  readonly URL: "URL";
  readonly USERNAME: "USERNAME";
  readonly US_BANK_ACCOUNT_NUMBER: "US_BANK_ACCOUNT_NUMBER";
  readonly US_BANK_ROUTING_NUMBER: "US_BANK_ROUTING_NUMBER";
  readonly US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER: "US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER";
  readonly US_PASSPORT_NUMBER: "US_PASSPORT_NUMBER";
  readonly US_SOCIAL_SECURITY_NUMBER: "US_SOCIAL_SECURITY_NUMBER";
  readonly VEHICLE_IDENTIFICATION_NUMBER: "VEHICLE_IDENTIFICATION_NUMBER";
};
export type GuardrailPiiEntityType =
  (typeof GuardrailPiiEntityType)[keyof typeof GuardrailPiiEntityType];
export interface GuardrailPiiEntityFilter {
  match: string | undefined;
  type: GuardrailPiiEntityType | undefined;
  action: GuardrailSensitiveInformationPolicyAction | undefined;
  detected?: boolean | undefined;
}
export interface GuardrailRegexFilter {
  name?: string | undefined;
  match?: string | undefined;
  regex?: string | undefined;
  action: GuardrailSensitiveInformationPolicyAction | undefined;
  detected?: boolean | undefined;
}
export interface GuardrailSensitiveInformationPolicyAssessment {
  piiEntities: GuardrailPiiEntityFilter[] | undefined;
  regexes: GuardrailRegexFilter[] | undefined;
}
export declare const GuardrailTopicPolicyAction: {
  readonly BLOCKED: "BLOCKED";
  readonly NONE: "NONE";
};
export type GuardrailTopicPolicyAction =
  (typeof GuardrailTopicPolicyAction)[keyof typeof GuardrailTopicPolicyAction];
export declare const GuardrailTopicType: {
  readonly DENY: "DENY";
};
export type GuardrailTopicType =
  (typeof GuardrailTopicType)[keyof typeof GuardrailTopicType];
export interface GuardrailTopic {
  name: string | undefined;
  type: GuardrailTopicType | undefined;
  action: GuardrailTopicPolicyAction | undefined;
  detected?: boolean | undefined;
}
export interface GuardrailTopicPolicyAssessment {
  topics: GuardrailTopic[] | undefined;
}
export declare const GuardrailWordPolicyAction: {
  readonly BLOCKED: "BLOCKED";
  readonly NONE: "NONE";
};
export type GuardrailWordPolicyAction =
  (typeof GuardrailWordPolicyAction)[keyof typeof GuardrailWordPolicyAction];
export interface GuardrailCustomWord {
  match: string | undefined;
  action: GuardrailWordPolicyAction | undefined;
  detected?: boolean | undefined;
}
export declare const GuardrailManagedWordType: {
  readonly PROFANITY: "PROFANITY";
};
export type GuardrailManagedWordType =
  (typeof GuardrailManagedWordType)[keyof typeof GuardrailManagedWordType];
export interface GuardrailManagedWord {
  match: string | undefined;
  type: GuardrailManagedWordType | undefined;
  action: GuardrailWordPolicyAction | undefined;
  detected?: boolean | undefined;
}
export interface GuardrailWordPolicyAssessment {
  customWords: GuardrailCustomWord[] | undefined;
  managedWordLists: GuardrailManagedWord[] | undefined;
}
export interface GuardrailAssessment {
  topicPolicy?: GuardrailTopicPolicyAssessment | undefined;
  contentPolicy?: GuardrailContentPolicyAssessment | undefined;
  wordPolicy?: GuardrailWordPolicyAssessment | undefined;
  sensitiveInformationPolicy?:
    | GuardrailSensitiveInformationPolicyAssessment
    | undefined;
  contextualGroundingPolicy?:
    | GuardrailContextualGroundingPolicyAssessment
    | undefined;
  automatedReasoningPolicy?:
    | GuardrailAutomatedReasoningPolicyAssessment
    | undefined;
  invocationMetrics?: GuardrailInvocationMetrics | undefined;
}
export interface GuardrailOutputContent {
  text?: string | undefined;
}
export interface ApplyGuardrailResponse {
  usage: GuardrailUsage | undefined;
  action: GuardrailAction | undefined;
  actionReason?: string | undefined;
  outputs: GuardrailOutputContent[] | undefined;
  assessments: GuardrailAssessment[] | undefined;
  guardrailCoverage?: GuardrailCoverage | undefined;
}
export declare const GuardrailTrace: {
  readonly DISABLED: "disabled";
  readonly ENABLED: "enabled";
  readonly ENABLED_FULL: "enabled_full";
};
export type GuardrailTrace =
  (typeof GuardrailTrace)[keyof typeof GuardrailTrace];
export interface GuardrailConfiguration {
  guardrailIdentifier: string | undefined;
  guardrailVersion: string | undefined;
  trace?: GuardrailTrace | undefined;
}
export interface InferenceConfiguration {
  maxTokens?: number | undefined;
  temperature?: number | undefined;
  topP?: number | undefined;
  stopSequences?: string[] | undefined;
}
export declare const CachePointType: {
  readonly DEFAULT: "default";
};
export type CachePointType =
  (typeof CachePointType)[keyof typeof CachePointType];
export interface CachePointBlock {
  type: CachePointType | undefined;
}
export interface DocumentCharLocation {
  documentIndex?: number | undefined;
  start?: number | undefined;
  end?: number | undefined;
}
export interface DocumentChunkLocation {
  documentIndex?: number | undefined;
  start?: number | undefined;
  end?: number | undefined;
}
export interface DocumentPageLocation {
  documentIndex?: number | undefined;
  start?: number | undefined;
  end?: number | undefined;
}
export type CitationLocation =
  | CitationLocation.DocumentCharMember
  | CitationLocation.DocumentChunkMember
  | CitationLocation.DocumentPageMember
  | CitationLocation.$UnknownMember;
export declare namespace CitationLocation {
  interface DocumentCharMember {
    documentChar: DocumentCharLocation;
    documentPage?: never;
    documentChunk?: never;
    $unknown?: never;
  }
  interface DocumentPageMember {
    documentChar?: never;
    documentPage: DocumentPageLocation;
    documentChunk?: never;
    $unknown?: never;
  }
  interface DocumentChunkMember {
    documentChar?: never;
    documentPage?: never;
    documentChunk: DocumentChunkLocation;
    $unknown?: never;
  }
  interface $UnknownMember {
    documentChar?: never;
    documentPage?: never;
    documentChunk?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    documentChar: (value: DocumentCharLocation) => T;
    documentPage: (value: DocumentPageLocation) => T;
    documentChunk: (value: DocumentChunkLocation) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: CitationLocation, visitor: Visitor<T>) => T;
}
export type CitationSourceContent =
  | CitationSourceContent.TextMember
  | CitationSourceContent.$UnknownMember;
export declare namespace CitationSourceContent {
  interface TextMember {
    text: string;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: CitationSourceContent, visitor: Visitor<T>) => T;
}
export interface Citation {
  title?: string | undefined;
  sourceContent?: CitationSourceContent[] | undefined;
  location?: CitationLocation | undefined;
}
export type CitationGeneratedContent =
  | CitationGeneratedContent.TextMember
  | CitationGeneratedContent.$UnknownMember;
export declare namespace CitationGeneratedContent {
  interface TextMember {
    text: string;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: CitationGeneratedContent, visitor: Visitor<T>) => T;
}
export interface CitationsContentBlock {
  content?: CitationGeneratedContent[] | undefined;
  citations?: Citation[] | undefined;
}
export interface CitationsConfig {
  enabled: boolean | undefined;
}
export declare const DocumentFormat: {
  readonly CSV: "csv";
  readonly DOC: "doc";
  readonly DOCX: "docx";
  readonly HTML: "html";
  readonly MD: "md";
  readonly PDF: "pdf";
  readonly TXT: "txt";
  readonly XLS: "xls";
  readonly XLSX: "xlsx";
};
export type DocumentFormat =
  (typeof DocumentFormat)[keyof typeof DocumentFormat];
export type DocumentContentBlock =
  | DocumentContentBlock.TextMember
  | DocumentContentBlock.$UnknownMember;
export declare namespace DocumentContentBlock {
  interface TextMember {
    text: string;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: DocumentContentBlock, visitor: Visitor<T>) => T;
}
export interface S3Location {
  uri: string | undefined;
  bucketOwner?: string | undefined;
}
export type DocumentSource =
  | DocumentSource.BytesMember
  | DocumentSource.ContentMember
  | DocumentSource.S3LocationMember
  | DocumentSource.TextMember
  | DocumentSource.$UnknownMember;
export declare namespace DocumentSource {
  interface BytesMember {
    bytes: Uint8Array;
    s3Location?: never;
    text?: never;
    content?: never;
    $unknown?: never;
  }
  interface S3LocationMember {
    bytes?: never;
    s3Location: S3Location;
    text?: never;
    content?: never;
    $unknown?: never;
  }
  interface TextMember {
    bytes?: never;
    s3Location?: never;
    text: string;
    content?: never;
    $unknown?: never;
  }
  interface ContentMember {
    bytes?: never;
    s3Location?: never;
    text?: never;
    content: DocumentContentBlock[];
    $unknown?: never;
  }
  interface $UnknownMember {
    bytes?: never;
    s3Location?: never;
    text?: never;
    content?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    bytes: (value: Uint8Array) => T;
    s3Location: (value: S3Location) => T;
    text: (value: string) => T;
    content: (value: DocumentContentBlock[]) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: DocumentSource, visitor: Visitor<T>) => T;
}
export interface DocumentBlock {
  format?: DocumentFormat | undefined;
  name: string | undefined;
  source: DocumentSource | undefined;
  context?: string | undefined;
  citations?: CitationsConfig | undefined;
}
export declare const GuardrailConverseImageFormat: {
  readonly JPEG: "jpeg";
  readonly PNG: "png";
};
export type GuardrailConverseImageFormat =
  (typeof GuardrailConverseImageFormat)[keyof typeof GuardrailConverseImageFormat];
export type GuardrailConverseImageSource =
  | GuardrailConverseImageSource.BytesMember
  | GuardrailConverseImageSource.$UnknownMember;
export declare namespace GuardrailConverseImageSource {
  interface BytesMember {
    bytes: Uint8Array;
    $unknown?: never;
  }
  interface $UnknownMember {
    bytes?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    bytes: (value: Uint8Array) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: GuardrailConverseImageSource,
    visitor: Visitor<T>
  ) => T;
}
export interface GuardrailConverseImageBlock {
  format: GuardrailConverseImageFormat | undefined;
  source: GuardrailConverseImageSource | undefined;
}
export declare const GuardrailConverseContentQualifier: {
  readonly GROUNDING_SOURCE: "grounding_source";
  readonly GUARD_CONTENT: "guard_content";
  readonly QUERY: "query";
};
export type GuardrailConverseContentQualifier =
  (typeof GuardrailConverseContentQualifier)[keyof typeof GuardrailConverseContentQualifier];
export interface GuardrailConverseTextBlock {
  text: string | undefined;
  qualifiers?: GuardrailConverseContentQualifier[] | undefined;
}
export type GuardrailConverseContentBlock =
  | GuardrailConverseContentBlock.ImageMember
  | GuardrailConverseContentBlock.TextMember
  | GuardrailConverseContentBlock.$UnknownMember;
export declare namespace GuardrailConverseContentBlock {
  interface TextMember {
    text: GuardrailConverseTextBlock;
    image?: never;
    $unknown?: never;
  }
  interface ImageMember {
    text?: never;
    image: GuardrailConverseImageBlock;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    image?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: GuardrailConverseTextBlock) => T;
    image: (value: GuardrailConverseImageBlock) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: GuardrailConverseContentBlock,
    visitor: Visitor<T>
  ) => T;
}
export declare const ImageFormat: {
  readonly GIF: "gif";
  readonly JPEG: "jpeg";
  readonly PNG: "png";
  readonly WEBP: "webp";
};
export type ImageFormat = (typeof ImageFormat)[keyof typeof ImageFormat];
export type ImageSource =
  | ImageSource.BytesMember
  | ImageSource.S3LocationMember
  | ImageSource.$UnknownMember;
export declare namespace ImageSource {
  interface BytesMember {
    bytes: Uint8Array;
    s3Location?: never;
    $unknown?: never;
  }
  interface S3LocationMember {
    bytes?: never;
    s3Location: S3Location;
    $unknown?: never;
  }
  interface $UnknownMember {
    bytes?: never;
    s3Location?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    bytes: (value: Uint8Array) => T;
    s3Location: (value: S3Location) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ImageSource, visitor: Visitor<T>) => T;
}
export interface ImageBlock {
  format: ImageFormat | undefined;
  source: ImageSource | undefined;
}
export interface ReasoningTextBlock {
  text: string | undefined;
  signature?: string | undefined;
}
export type ReasoningContentBlock =
  | ReasoningContentBlock.ReasoningTextMember
  | ReasoningContentBlock.RedactedContentMember
  | ReasoningContentBlock.$UnknownMember;
export declare namespace ReasoningContentBlock {
  interface ReasoningTextMember {
    reasoningText: ReasoningTextBlock;
    redactedContent?: never;
    $unknown?: never;
  }
  interface RedactedContentMember {
    reasoningText?: never;
    redactedContent: Uint8Array;
    $unknown?: never;
  }
  interface $UnknownMember {
    reasoningText?: never;
    redactedContent?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    reasoningText: (value: ReasoningTextBlock) => T;
    redactedContent: (value: Uint8Array) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ReasoningContentBlock, visitor: Visitor<T>) => T;
}
export declare const VideoFormat: {
  readonly FLV: "flv";
  readonly MKV: "mkv";
  readonly MOV: "mov";
  readonly MP4: "mp4";
  readonly MPEG: "mpeg";
  readonly MPG: "mpg";
  readonly THREE_GP: "three_gp";
  readonly WEBM: "webm";
  readonly WMV: "wmv";
};
export type VideoFormat = (typeof VideoFormat)[keyof typeof VideoFormat];
export type VideoSource =
  | VideoSource.BytesMember
  | VideoSource.S3LocationMember
  | VideoSource.$UnknownMember;
export declare namespace VideoSource {
  interface BytesMember {
    bytes: Uint8Array;
    s3Location?: never;
    $unknown?: never;
  }
  interface S3LocationMember {
    bytes?: never;
    s3Location: S3Location;
    $unknown?: never;
  }
  interface $UnknownMember {
    bytes?: never;
    s3Location?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    bytes: (value: Uint8Array) => T;
    s3Location: (value: S3Location) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: VideoSource, visitor: Visitor<T>) => T;
}
export interface VideoBlock {
  format: VideoFormat | undefined;
  source: VideoSource | undefined;
}
export type ToolResultContentBlock =
  | ToolResultContentBlock.DocumentMember
  | ToolResultContentBlock.ImageMember
  | ToolResultContentBlock.JsonMember
  | ToolResultContentBlock.TextMember
  | ToolResultContentBlock.VideoMember
  | ToolResultContentBlock.$UnknownMember;
export declare namespace ToolResultContentBlock {
  interface JsonMember {
    json: __DocumentType;
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    $unknown?: never;
  }
  interface TextMember {
    json?: never;
    text: string;
    image?: never;
    document?: never;
    video?: never;
    $unknown?: never;
  }
  interface ImageMember {
    json?: never;
    text?: never;
    image: ImageBlock;
    document?: never;
    video?: never;
    $unknown?: never;
  }
  interface DocumentMember {
    json?: never;
    text?: never;
    image?: never;
    document: DocumentBlock;
    video?: never;
    $unknown?: never;
  }
  interface VideoMember {
    json?: never;
    text?: never;
    image?: never;
    document?: never;
    video: VideoBlock;
    $unknown?: never;
  }
  interface $UnknownMember {
    json?: never;
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    json: (value: __DocumentType) => T;
    text: (value: string) => T;
    image: (value: ImageBlock) => T;
    document: (value: DocumentBlock) => T;
    video: (value: VideoBlock) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ToolResultContentBlock, visitor: Visitor<T>) => T;
}
export declare const ToolResultStatus: {
  readonly ERROR: "error";
  readonly SUCCESS: "success";
};
export type ToolResultStatus =
  (typeof ToolResultStatus)[keyof typeof ToolResultStatus];
export interface ToolResultBlock {
  toolUseId: string | undefined;
  content: ToolResultContentBlock[] | undefined;
  status?: ToolResultStatus | undefined;
}
export interface ToolUseBlock {
  toolUseId: string | undefined;
  name: string | undefined;
  input: __DocumentType | undefined;
}
export type ContentBlock =
  | ContentBlock.CachePointMember
  | ContentBlock.CitationsContentMember
  | ContentBlock.DocumentMember
  | ContentBlock.GuardContentMember
  | ContentBlock.ImageMember
  | ContentBlock.ReasoningContentMember
  | ContentBlock.TextMember
  | ContentBlock.ToolResultMember
  | ContentBlock.ToolUseMember
  | ContentBlock.VideoMember
  | ContentBlock.$UnknownMember;
export declare namespace ContentBlock {
  interface TextMember {
    text: string;
    image?: never;
    document?: never;
    video?: never;
    toolUse?: never;
    toolResult?: never;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown?: never;
  }
  interface ImageMember {
    text?: never;
    image: ImageBlock;
    document?: never;
    video?: never;
    toolUse?: never;
    toolResult?: never;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown?: never;
  }
  interface DocumentMember {
    text?: never;
    image?: never;
    document: DocumentBlock;
    video?: never;
    toolUse?: never;
    toolResult?: never;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown?: never;
  }
  interface VideoMember {
    text?: never;
    image?: never;
    document?: never;
    video: VideoBlock;
    toolUse?: never;
    toolResult?: never;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown?: never;
  }
  interface ToolUseMember {
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    toolUse: ToolUseBlock;
    toolResult?: never;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown?: never;
  }
  interface ToolResultMember {
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    toolUse?: never;
    toolResult: ToolResultBlock;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown?: never;
  }
  interface GuardContentMember {
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    toolUse?: never;
    toolResult?: never;
    guardContent: GuardrailConverseContentBlock;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown?: never;
  }
  interface CachePointMember {
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    toolUse?: never;
    toolResult?: never;
    guardContent?: never;
    cachePoint: CachePointBlock;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown?: never;
  }
  interface ReasoningContentMember {
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    toolUse?: never;
    toolResult?: never;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent: ReasoningContentBlock;
    citationsContent?: never;
    $unknown?: never;
  }
  interface CitationsContentMember {
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    toolUse?: never;
    toolResult?: never;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent: CitationsContentBlock;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    image?: never;
    document?: never;
    video?: never;
    toolUse?: never;
    toolResult?: never;
    guardContent?: never;
    cachePoint?: never;
    reasoningContent?: never;
    citationsContent?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    image: (value: ImageBlock) => T;
    document: (value: DocumentBlock) => T;
    video: (value: VideoBlock) => T;
    toolUse: (value: ToolUseBlock) => T;
    toolResult: (value: ToolResultBlock) => T;
    guardContent: (value: GuardrailConverseContentBlock) => T;
    cachePoint: (value: CachePointBlock) => T;
    reasoningContent: (value: ReasoningContentBlock) => T;
    citationsContent: (value: CitationsContentBlock) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ContentBlock, visitor: Visitor<T>) => T;
}
export declare const ConversationRole: {
  readonly ASSISTANT: "assistant";
  readonly USER: "user";
};
export type ConversationRole =
  (typeof ConversationRole)[keyof typeof ConversationRole];
export interface Message {
  role: ConversationRole | undefined;
  content: ContentBlock[] | undefined;
}
export declare const PerformanceConfigLatency: {
  readonly OPTIMIZED: "optimized";
  readonly STANDARD: "standard";
};
export type PerformanceConfigLatency =
  (typeof PerformanceConfigLatency)[keyof typeof PerformanceConfigLatency];
export interface PerformanceConfiguration {
  latency?: PerformanceConfigLatency | undefined;
}
export type PromptVariableValues =
  | PromptVariableValues.TextMember
  | PromptVariableValues.$UnknownMember;
export declare namespace PromptVariableValues {
  interface TextMember {
    text: string;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: PromptVariableValues, visitor: Visitor<T>) => T;
}
export type SystemContentBlock =
  | SystemContentBlock.CachePointMember
  | SystemContentBlock.GuardContentMember
  | SystemContentBlock.TextMember
  | SystemContentBlock.$UnknownMember;
export declare namespace SystemContentBlock {
  interface TextMember {
    text: string;
    guardContent?: never;
    cachePoint?: never;
    $unknown?: never;
  }
  interface GuardContentMember {
    text?: never;
    guardContent: GuardrailConverseContentBlock;
    cachePoint?: never;
    $unknown?: never;
  }
  interface CachePointMember {
    text?: never;
    guardContent?: never;
    cachePoint: CachePointBlock;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    guardContent?: never;
    cachePoint?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    guardContent: (value: GuardrailConverseContentBlock) => T;
    cachePoint: (value: CachePointBlock) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: SystemContentBlock, visitor: Visitor<T>) => T;
}
export interface AnyToolChoice {}
export interface AutoToolChoice {}
export interface SpecificToolChoice {
  name: string | undefined;
}
export type ToolChoice =
  | ToolChoice.AnyMember
  | ToolChoice.AutoMember
  | ToolChoice.ToolMember
  | ToolChoice.$UnknownMember;
export declare namespace ToolChoice {
  interface AutoMember {
    auto: AutoToolChoice;
    any?: never;
    tool?: never;
    $unknown?: never;
  }
  interface AnyMember {
    auto?: never;
    any: AnyToolChoice;
    tool?: never;
    $unknown?: never;
  }
  interface ToolMember {
    auto?: never;
    any?: never;
    tool: SpecificToolChoice;
    $unknown?: never;
  }
  interface $UnknownMember {
    auto?: never;
    any?: never;
    tool?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    auto: (value: AutoToolChoice) => T;
    any: (value: AnyToolChoice) => T;
    tool: (value: SpecificToolChoice) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ToolChoice, visitor: Visitor<T>) => T;
}
export type ToolInputSchema =
  | ToolInputSchema.JsonMember
  | ToolInputSchema.$UnknownMember;
export declare namespace ToolInputSchema {
  interface JsonMember {
    json: __DocumentType;
    $unknown?: never;
  }
  interface $UnknownMember {
    json?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    json: (value: __DocumentType) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ToolInputSchema, visitor: Visitor<T>) => T;
}
export interface ToolSpecification {
  name: string | undefined;
  description?: string | undefined;
  inputSchema: ToolInputSchema | undefined;
}
export type Tool =
  | Tool.CachePointMember
  | Tool.ToolSpecMember
  | Tool.$UnknownMember;
export declare namespace Tool {
  interface ToolSpecMember {
    toolSpec: ToolSpecification;
    cachePoint?: never;
    $unknown?: never;
  }
  interface CachePointMember {
    toolSpec?: never;
    cachePoint: CachePointBlock;
    $unknown?: never;
  }
  interface $UnknownMember {
    toolSpec?: never;
    cachePoint?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    toolSpec: (value: ToolSpecification) => T;
    cachePoint: (value: CachePointBlock) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: Tool, visitor: Visitor<T>) => T;
}
export interface ToolConfiguration {
  tools: Tool[] | undefined;
  toolChoice?: ToolChoice | undefined;
}
export interface ConverseRequest {
  modelId: string | undefined;
  messages?: Message[] | undefined;
  system?: SystemContentBlock[] | undefined;
  inferenceConfig?: InferenceConfiguration | undefined;
  toolConfig?: ToolConfiguration | undefined;
  guardrailConfig?: GuardrailConfiguration | undefined;
  additionalModelRequestFields?: __DocumentType | undefined;
  promptVariables?: Record<string, PromptVariableValues> | undefined;
  additionalModelResponseFieldPaths?: string[] | undefined;
  requestMetadata?: Record<string, string> | undefined;
  performanceConfig?: PerformanceConfiguration | undefined;
}
export interface ConverseMetrics {
  latencyMs: number | undefined;
}
export type ConverseOutput =
  | ConverseOutput.MessageMember
  | ConverseOutput.$UnknownMember;
export declare namespace ConverseOutput {
  interface MessageMember {
    message: Message;
    $unknown?: never;
  }
  interface $UnknownMember {
    message?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    message: (value: Message) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ConverseOutput, visitor: Visitor<T>) => T;
}
export declare const StopReason: {
  readonly CONTENT_FILTERED: "content_filtered";
  readonly END_TURN: "end_turn";
  readonly GUARDRAIL_INTERVENED: "guardrail_intervened";
  readonly MAX_TOKENS: "max_tokens";
  readonly STOP_SEQUENCE: "stop_sequence";
  readonly TOOL_USE: "tool_use";
};
export type StopReason = (typeof StopReason)[keyof typeof StopReason];
export interface GuardrailTraceAssessment {
  modelOutput?: string[] | undefined;
  inputAssessment?: Record<string, GuardrailAssessment> | undefined;
  outputAssessments?: Record<string, GuardrailAssessment[]> | undefined;
  actionReason?: string | undefined;
}
export interface PromptRouterTrace {
  invokedModelId?: string | undefined;
}
export interface ConverseTrace {
  guardrail?: GuardrailTraceAssessment | undefined;
  promptRouter?: PromptRouterTrace | undefined;
}
export interface TokenUsage {
  inputTokens: number | undefined;
  outputTokens: number | undefined;
  totalTokens: number | undefined;
  cacheReadInputTokens?: number | undefined;
  cacheWriteInputTokens?: number | undefined;
}
export interface ConverseResponse {
  output: ConverseOutput | undefined;
  stopReason: StopReason | undefined;
  usage: TokenUsage | undefined;
  metrics: ConverseMetrics | undefined;
  additionalModelResponseFields?: __DocumentType | undefined;
  trace?: ConverseTrace | undefined;
  performanceConfig?: PerformanceConfiguration | undefined;
}
export declare class ModelErrorException extends __BaseException {
  readonly name: "ModelErrorException";
  readonly $fault: "client";
  originalStatusCode?: number | undefined;
  resourceName?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ModelErrorException, __BaseException>
  );
}
export declare class ModelNotReadyException extends __BaseException {
  readonly name: "ModelNotReadyException";
  readonly $fault: "client";
  $retryable: {};
  constructor(
    opts: __ExceptionOptionType<ModelNotReadyException, __BaseException>
  );
}
export declare class ModelTimeoutException extends __BaseException {
  readonly name: "ModelTimeoutException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<ModelTimeoutException, __BaseException>
  );
}
export declare const GuardrailStreamProcessingMode: {
  readonly ASYNC: "async";
  readonly SYNC: "sync";
};
export type GuardrailStreamProcessingMode =
  (typeof GuardrailStreamProcessingMode)[keyof typeof GuardrailStreamProcessingMode];
export interface GuardrailStreamConfiguration {
  guardrailIdentifier: string | undefined;
  guardrailVersion: string | undefined;
  trace?: GuardrailTrace | undefined;
  streamProcessingMode?: GuardrailStreamProcessingMode | undefined;
}
export interface ConverseStreamRequest {
  modelId: string | undefined;
  messages?: Message[] | undefined;
  system?: SystemContentBlock[] | undefined;
  inferenceConfig?: InferenceConfiguration | undefined;
  toolConfig?: ToolConfiguration | undefined;
  guardrailConfig?: GuardrailStreamConfiguration | undefined;
  additionalModelRequestFields?: __DocumentType | undefined;
  promptVariables?: Record<string, PromptVariableValues> | undefined;
  additionalModelResponseFieldPaths?: string[] | undefined;
  requestMetadata?: Record<string, string> | undefined;
  performanceConfig?: PerformanceConfiguration | undefined;
}
export interface CitationSourceContentDelta {
  text?: string | undefined;
}
export interface CitationsDelta {
  title?: string | undefined;
  sourceContent?: CitationSourceContentDelta[] | undefined;
  location?: CitationLocation | undefined;
}
export type ReasoningContentBlockDelta =
  | ReasoningContentBlockDelta.RedactedContentMember
  | ReasoningContentBlockDelta.SignatureMember
  | ReasoningContentBlockDelta.TextMember
  | ReasoningContentBlockDelta.$UnknownMember;
export declare namespace ReasoningContentBlockDelta {
  interface TextMember {
    text: string;
    redactedContent?: never;
    signature?: never;
    $unknown?: never;
  }
  interface RedactedContentMember {
    text?: never;
    redactedContent: Uint8Array;
    signature?: never;
    $unknown?: never;
  }
  interface SignatureMember {
    text?: never;
    redactedContent?: never;
    signature: string;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    redactedContent?: never;
    signature?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    redactedContent: (value: Uint8Array) => T;
    signature: (value: string) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ReasoningContentBlockDelta, visitor: Visitor<T>) => T;
}
export interface ToolUseBlockDelta {
  input: string | undefined;
}
export type ContentBlockDelta =
  | ContentBlockDelta.CitationMember
  | ContentBlockDelta.ReasoningContentMember
  | ContentBlockDelta.TextMember
  | ContentBlockDelta.ToolUseMember
  | ContentBlockDelta.$UnknownMember;
export declare namespace ContentBlockDelta {
  interface TextMember {
    text: string;
    toolUse?: never;
    reasoningContent?: never;
    citation?: never;
    $unknown?: never;
  }
  interface ToolUseMember {
    text?: never;
    toolUse: ToolUseBlockDelta;
    reasoningContent?: never;
    citation?: never;
    $unknown?: never;
  }
  interface ReasoningContentMember {
    text?: never;
    toolUse?: never;
    reasoningContent: ReasoningContentBlockDelta;
    citation?: never;
    $unknown?: never;
  }
  interface CitationMember {
    text?: never;
    toolUse?: never;
    reasoningContent?: never;
    citation: CitationsDelta;
    $unknown?: never;
  }
  interface $UnknownMember {
    text?: never;
    toolUse?: never;
    reasoningContent?: never;
    citation?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    text: (value: string) => T;
    toolUse: (value: ToolUseBlockDelta) => T;
    reasoningContent: (value: ReasoningContentBlockDelta) => T;
    citation: (value: CitationsDelta) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ContentBlockDelta, visitor: Visitor<T>) => T;
}
export interface ContentBlockDeltaEvent {
  delta: ContentBlockDelta | undefined;
  contentBlockIndex: number | undefined;
}
export interface ToolUseBlockStart {
  toolUseId: string | undefined;
  name: string | undefined;
}
export type ContentBlockStart =
  | ContentBlockStart.ToolUseMember
  | ContentBlockStart.$UnknownMember;
export declare namespace ContentBlockStart {
  interface ToolUseMember {
    toolUse: ToolUseBlockStart;
    $unknown?: never;
  }
  interface $UnknownMember {
    toolUse?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    toolUse: (value: ToolUseBlockStart) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ContentBlockStart, visitor: Visitor<T>) => T;
}
export interface ContentBlockStartEvent {
  start: ContentBlockStart | undefined;
  contentBlockIndex: number | undefined;
}
export interface ContentBlockStopEvent {
  contentBlockIndex: number | undefined;
}
export interface MessageStartEvent {
  role: ConversationRole | undefined;
}
export interface MessageStopEvent {
  stopReason: StopReason | undefined;
  additionalModelResponseFields?: __DocumentType | undefined;
}
export interface ConverseStreamMetrics {
  latencyMs: number | undefined;
}
export interface ConverseStreamTrace {
  guardrail?: GuardrailTraceAssessment | undefined;
  promptRouter?: PromptRouterTrace | undefined;
}
export interface ConverseStreamMetadataEvent {
  usage: TokenUsage | undefined;
  metrics: ConverseStreamMetrics | undefined;
  trace?: ConverseStreamTrace | undefined;
  performanceConfig?: PerformanceConfiguration | undefined;
}
export declare class ModelStreamErrorException extends __BaseException {
  readonly name: "ModelStreamErrorException";
  readonly $fault: "client";
  originalStatusCode?: number | undefined;
  originalMessage?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ModelStreamErrorException, __BaseException>
  );
}
export type ConverseStreamOutput =
  | ConverseStreamOutput.ContentBlockDeltaMember
  | ConverseStreamOutput.ContentBlockStartMember
  | ConverseStreamOutput.ContentBlockStopMember
  | ConverseStreamOutput.InternalServerExceptionMember
  | ConverseStreamOutput.MessageStartMember
  | ConverseStreamOutput.MessageStopMember
  | ConverseStreamOutput.MetadataMember
  | ConverseStreamOutput.ModelStreamErrorExceptionMember
  | ConverseStreamOutput.ServiceUnavailableExceptionMember
  | ConverseStreamOutput.ThrottlingExceptionMember
  | ConverseStreamOutput.ValidationExceptionMember
  | ConverseStreamOutput.$UnknownMember;
export declare namespace ConverseStreamOutput {
  interface MessageStartMember {
    messageStart: MessageStartEvent;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ContentBlockStartMember {
    messageStart?: never;
    contentBlockStart: ContentBlockStartEvent;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ContentBlockDeltaMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta: ContentBlockDeltaEvent;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ContentBlockStopMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop: ContentBlockStopEvent;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface MessageStopMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop: MessageStopEvent;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface MetadataMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata: ConverseStreamMetadataEvent;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface InternalServerExceptionMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException: InternalServerException;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ModelStreamErrorExceptionMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException: ModelStreamErrorException;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ValidationExceptionMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException: ValidationException;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ThrottlingExceptionMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException: ThrottlingException;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ServiceUnavailableExceptionMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException: ServiceUnavailableException;
    $unknown?: never;
  }
  interface $UnknownMember {
    messageStart?: never;
    contentBlockStart?: never;
    contentBlockDelta?: never;
    contentBlockStop?: never;
    messageStop?: never;
    metadata?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    serviceUnavailableException?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    messageStart: (value: MessageStartEvent) => T;
    contentBlockStart: (value: ContentBlockStartEvent) => T;
    contentBlockDelta: (value: ContentBlockDeltaEvent) => T;
    contentBlockStop: (value: ContentBlockStopEvent) => T;
    messageStop: (value: MessageStopEvent) => T;
    metadata: (value: ConverseStreamMetadataEvent) => T;
    internalServerException: (value: InternalServerException) => T;
    modelStreamErrorException: (value: ModelStreamErrorException) => T;
    validationException: (value: ValidationException) => T;
    throttlingException: (value: ThrottlingException) => T;
    serviceUnavailableException: (value: ServiceUnavailableException) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ConverseStreamOutput, visitor: Visitor<T>) => T;
}
export interface ConverseStreamResponse {
  stream?: AsyncIterable<ConverseStreamOutput> | undefined;
}
export declare const Trace: {
  readonly DISABLED: "DISABLED";
  readonly ENABLED: "ENABLED";
  readonly ENABLED_FULL: "ENABLED_FULL";
};
export type Trace = (typeof Trace)[keyof typeof Trace];
export interface InvokeModelRequest {
  body?: Uint8Array | undefined;
  contentType?: string | undefined;
  accept?: string | undefined;
  modelId: string | undefined;
  trace?: Trace | undefined;
  guardrailIdentifier?: string | undefined;
  guardrailVersion?: string | undefined;
  performanceConfigLatency?: PerformanceConfigLatency | undefined;
}
export interface InvokeModelResponse {
  body: Uint8Array | undefined;
  contentType: string | undefined;
  performanceConfigLatency?: PerformanceConfigLatency | undefined;
}
export interface BidirectionalInputPayloadPart {
  bytes?: Uint8Array | undefined;
}
export type InvokeModelWithBidirectionalStreamInput =
  | InvokeModelWithBidirectionalStreamInput.ChunkMember
  | InvokeModelWithBidirectionalStreamInput.$UnknownMember;
export declare namespace InvokeModelWithBidirectionalStreamInput {
  interface ChunkMember {
    chunk: BidirectionalInputPayloadPart;
    $unknown?: never;
  }
  interface $UnknownMember {
    chunk?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    chunk: (value: BidirectionalInputPayloadPart) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: InvokeModelWithBidirectionalStreamInput,
    visitor: Visitor<T>
  ) => T;
}
export interface InvokeModelWithBidirectionalStreamRequest {
  modelId: string | undefined;
  body: AsyncIterable<InvokeModelWithBidirectionalStreamInput> | undefined;
}
export interface BidirectionalOutputPayloadPart {
  bytes?: Uint8Array | undefined;
}
export type InvokeModelWithBidirectionalStreamOutput =
  | InvokeModelWithBidirectionalStreamOutput.ChunkMember
  | InvokeModelWithBidirectionalStreamOutput.InternalServerExceptionMember
  | InvokeModelWithBidirectionalStreamOutput.ModelStreamErrorExceptionMember
  | InvokeModelWithBidirectionalStreamOutput.ModelTimeoutExceptionMember
  | InvokeModelWithBidirectionalStreamOutput.ServiceUnavailableExceptionMember
  | InvokeModelWithBidirectionalStreamOutput.ThrottlingExceptionMember
  | InvokeModelWithBidirectionalStreamOutput.ValidationExceptionMember
  | InvokeModelWithBidirectionalStreamOutput.$UnknownMember;
export declare namespace InvokeModelWithBidirectionalStreamOutput {
  interface ChunkMember {
    chunk: BidirectionalOutputPayloadPart;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface InternalServerExceptionMember {
    chunk?: never;
    internalServerException: InternalServerException;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ModelStreamErrorExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException: ModelStreamErrorException;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ValidationExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException: ValidationException;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ThrottlingExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException: ThrottlingException;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ModelTimeoutExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException: ModelTimeoutException;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ServiceUnavailableExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException: ServiceUnavailableException;
    $unknown?: never;
  }
  interface $UnknownMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    chunk: (value: BidirectionalOutputPayloadPart) => T;
    internalServerException: (value: InternalServerException) => T;
    modelStreamErrorException: (value: ModelStreamErrorException) => T;
    validationException: (value: ValidationException) => T;
    throttlingException: (value: ThrottlingException) => T;
    modelTimeoutException: (value: ModelTimeoutException) => T;
    serviceUnavailableException: (value: ServiceUnavailableException) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: InvokeModelWithBidirectionalStreamOutput,
    visitor: Visitor<T>
  ) => T;
}
export interface InvokeModelWithBidirectionalStreamResponse {
  body: AsyncIterable<InvokeModelWithBidirectionalStreamOutput> | undefined;
}
export interface InvokeModelWithResponseStreamRequest {
  body?: Uint8Array | undefined;
  contentType?: string | undefined;
  accept?: string | undefined;
  modelId: string | undefined;
  trace?: Trace | undefined;
  guardrailIdentifier?: string | undefined;
  guardrailVersion?: string | undefined;
  performanceConfigLatency?: PerformanceConfigLatency | undefined;
}
export interface PayloadPart {
  bytes?: Uint8Array | undefined;
}
export type ResponseStream =
  | ResponseStream.ChunkMember
  | ResponseStream.InternalServerExceptionMember
  | ResponseStream.ModelStreamErrorExceptionMember
  | ResponseStream.ModelTimeoutExceptionMember
  | ResponseStream.ServiceUnavailableExceptionMember
  | ResponseStream.ThrottlingExceptionMember
  | ResponseStream.ValidationExceptionMember
  | ResponseStream.$UnknownMember;
export declare namespace ResponseStream {
  interface ChunkMember {
    chunk: PayloadPart;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface InternalServerExceptionMember {
    chunk?: never;
    internalServerException: InternalServerException;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ModelStreamErrorExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException: ModelStreamErrorException;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ValidationExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException: ValidationException;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ThrottlingExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException: ThrottlingException;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ModelTimeoutExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException: ModelTimeoutException;
    serviceUnavailableException?: never;
    $unknown?: never;
  }
  interface ServiceUnavailableExceptionMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException: ServiceUnavailableException;
    $unknown?: never;
  }
  interface $UnknownMember {
    chunk?: never;
    internalServerException?: never;
    modelStreamErrorException?: never;
    validationException?: never;
    throttlingException?: never;
    modelTimeoutException?: never;
    serviceUnavailableException?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    chunk: (value: PayloadPart) => T;
    internalServerException: (value: InternalServerException) => T;
    modelStreamErrorException: (value: ModelStreamErrorException) => T;
    validationException: (value: ValidationException) => T;
    throttlingException: (value: ThrottlingException) => T;
    modelTimeoutException: (value: ModelTimeoutException) => T;
    serviceUnavailableException: (value: ServiceUnavailableException) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: ResponseStream, visitor: Visitor<T>) => T;
}
export interface InvokeModelWithResponseStreamResponse {
  body: AsyncIterable<ResponseStream> | undefined;
  contentType: string | undefined;
  performanceConfigLatency?: PerformanceConfigLatency | undefined;
}
export interface ConverseTokensRequest {
  messages?: Message[] | undefined;
  system?: SystemContentBlock[] | undefined;
}
export interface InvokeModelTokensRequest {
  body: Uint8Array | undefined;
}
export type CountTokensInput =
  | CountTokensInput.ConverseMember
  | CountTokensInput.InvokeModelMember
  | CountTokensInput.$UnknownMember;
export declare namespace CountTokensInput {
  interface InvokeModelMember {
    invokeModel: InvokeModelTokensRequest;
    converse?: never;
    $unknown?: never;
  }
  interface ConverseMember {
    invokeModel?: never;
    converse: ConverseTokensRequest;
    $unknown?: never;
  }
  interface $UnknownMember {
    invokeModel?: never;
    converse?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    invokeModel: (value: InvokeModelTokensRequest) => T;
    converse: (value: ConverseTokensRequest) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: CountTokensInput, visitor: Visitor<T>) => T;
}
export interface CountTokensRequest {
  modelId: string | undefined;
  input: CountTokensInput | undefined;
}
export interface CountTokensResponse {
  inputTokens: number | undefined;
}
export declare const GetAsyncInvokeResponseFilterSensitiveLog: (
  obj: GetAsyncInvokeResponse
) => any;
export declare const AsyncInvokeSummaryFilterSensitiveLog: (
  obj: AsyncInvokeSummary
) => any;
export declare const ListAsyncInvokesResponseFilterSensitiveLog: (
  obj: ListAsyncInvokesResponse
) => any;
export declare const StartAsyncInvokeRequestFilterSensitiveLog: (
  obj: StartAsyncInvokeRequest
) => any;
export declare const GuardrailImageSourceFilterSensitiveLog: (
  obj: GuardrailImageSource
) => any;
export declare const GuardrailImageBlockFilterSensitiveLog: (
  obj: GuardrailImageBlock
) => any;
export declare const GuardrailContentBlockFilterSensitiveLog: (
  obj: GuardrailContentBlock
) => any;
export declare const ApplyGuardrailRequestFilterSensitiveLog: (
  obj: ApplyGuardrailRequest
) => any;
export declare const GuardrailAutomatedReasoningStatementFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningStatement
) => any;
export declare const GuardrailAutomatedReasoningLogicWarningFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningLogicWarning
) => any;
export declare const GuardrailAutomatedReasoningInputTextReferenceFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningInputTextReference
) => any;
export declare const GuardrailAutomatedReasoningTranslationFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningTranslation
) => any;
export declare const GuardrailAutomatedReasoningImpossibleFindingFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningImpossibleFinding
) => any;
export declare const GuardrailAutomatedReasoningInvalidFindingFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningInvalidFinding
) => any;
export declare const GuardrailAutomatedReasoningScenarioFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningScenario
) => any;
export declare const GuardrailAutomatedReasoningSatisfiableFindingFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningSatisfiableFinding
) => any;
export declare const GuardrailAutomatedReasoningTranslationOptionFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningTranslationOption
) => any;
export declare const GuardrailAutomatedReasoningTranslationAmbiguousFindingFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningTranslationAmbiguousFinding
) => any;
export declare const GuardrailAutomatedReasoningValidFindingFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningValidFinding
) => any;
export declare const GuardrailAutomatedReasoningFindingFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningFinding
) => any;
export declare const GuardrailAutomatedReasoningPolicyAssessmentFilterSensitiveLog: (
  obj: GuardrailAutomatedReasoningPolicyAssessment
) => any;
export declare const GuardrailAssessmentFilterSensitiveLog: (
  obj: GuardrailAssessment
) => any;
export declare const ApplyGuardrailResponseFilterSensitiveLog: (
  obj: ApplyGuardrailResponse
) => any;
export declare const GuardrailConverseImageSourceFilterSensitiveLog: (
  obj: GuardrailConverseImageSource
) => any;
export declare const GuardrailConverseImageBlockFilterSensitiveLog: (
  obj: GuardrailConverseImageBlock
) => any;
export declare const GuardrailConverseContentBlockFilterSensitiveLog: (
  obj: GuardrailConverseContentBlock
) => any;
export declare const ReasoningTextBlockFilterSensitiveLog: (
  obj: ReasoningTextBlock
) => any;
export declare const ReasoningContentBlockFilterSensitiveLog: (
  obj: ReasoningContentBlock
) => any;
export declare const ContentBlockFilterSensitiveLog: (obj: ContentBlock) => any;
export declare const MessageFilterSensitiveLog: (obj: Message) => any;
export declare const SystemContentBlockFilterSensitiveLog: (
  obj: SystemContentBlock
) => any;
export declare const ConverseRequestFilterSensitiveLog: (
  obj: ConverseRequest
) => any;
export declare const ConverseOutputFilterSensitiveLog: (
  obj: ConverseOutput
) => any;
export declare const GuardrailTraceAssessmentFilterSensitiveLog: (
  obj: GuardrailTraceAssessment
) => any;
export declare const ConverseTraceFilterSensitiveLog: (
  obj: ConverseTrace
) => any;
export declare const ConverseResponseFilterSensitiveLog: (
  obj: ConverseResponse
) => any;
export declare const ConverseStreamRequestFilterSensitiveLog: (
  obj: ConverseStreamRequest
) => any;
export declare const ReasoningContentBlockDeltaFilterSensitiveLog: (
  obj: ReasoningContentBlockDelta
) => any;
export declare const ContentBlockDeltaFilterSensitiveLog: (
  obj: ContentBlockDelta
) => any;
export declare const ContentBlockDeltaEventFilterSensitiveLog: (
  obj: ContentBlockDeltaEvent
) => any;
export declare const ConverseStreamTraceFilterSensitiveLog: (
  obj: ConverseStreamTrace
) => any;
export declare const ConverseStreamMetadataEventFilterSensitiveLog: (
  obj: ConverseStreamMetadataEvent
) => any;
export declare const ConverseStreamOutputFilterSensitiveLog: (
  obj: ConverseStreamOutput
) => any;
export declare const ConverseStreamResponseFilterSensitiveLog: (
  obj: ConverseStreamResponse
) => any;
export declare const InvokeModelRequestFilterSensitiveLog: (
  obj: InvokeModelRequest
) => any;
export declare const InvokeModelResponseFilterSensitiveLog: (
  obj: InvokeModelResponse
) => any;
export declare const BidirectionalInputPayloadPartFilterSensitiveLog: (
  obj: BidirectionalInputPayloadPart
) => any;
export declare const InvokeModelWithBidirectionalStreamInputFilterSensitiveLog: (
  obj: InvokeModelWithBidirectionalStreamInput
) => any;
export declare const InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog: (
  obj: InvokeModelWithBidirectionalStreamRequest
) => any;
export declare const BidirectionalOutputPayloadPartFilterSensitiveLog: (
  obj: BidirectionalOutputPayloadPart
) => any;
export declare const InvokeModelWithBidirectionalStreamOutputFilterSensitiveLog: (
  obj: InvokeModelWithBidirectionalStreamOutput
) => any;
export declare const InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog: (
  obj: InvokeModelWithBidirectionalStreamResponse
) => any;
export declare const InvokeModelWithResponseStreamRequestFilterSensitiveLog: (
  obj: InvokeModelWithResponseStreamRequest
) => any;
export declare const PayloadPartFilterSensitiveLog: (obj: PayloadPart) => any;
export declare const ResponseStreamFilterSensitiveLog: (
  obj: ResponseStream
) => any;
export declare const InvokeModelWithResponseStreamResponseFilterSensitiveLog: (
  obj: InvokeModelWithResponseStreamResponse
) => any;
export declare const ConverseTokensRequestFilterSensitiveLog: (
  obj: ConverseTokensRequest
) => any;
export declare const InvokeModelTokensRequestFilterSensitiveLog: (
  obj: InvokeModelTokensRequest
) => any;
export declare const CountTokensInputFilterSensitiveLog: (
  obj: CountTokensInput
) => any;
export declare const CountTokensRequestFilterSensitiveLog: (
  obj: CountTokensRequest
) => any;
