import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { FunctionConfigurationFilterSensitiveLog, UpdateFunctionConfigurationRequestFilterSensitiveLog, } from "../models/models_0";
import { de_UpdateFunctionConfigurationCommand, se_UpdateFunctionConfigurationCommand, } from "../protocols/Aws_restJson1";
export { $Command };
export class UpdateFunctionConfigurationCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "UpdateFunctionConfiguration", {})
    .n("LambdaClient", "UpdateFunctionConfigurationCommand")
    .f(UpdateFunctionConfigurationRequestFilterSensitiveLog, FunctionConfigurationFilterSensitiveLog)
    .ser(se_UpdateFunctionConfigurationCommand)
    .de(de_UpdateFunctionConfigurationCommand)
    .build() {
}
