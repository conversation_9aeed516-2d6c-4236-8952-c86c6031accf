import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { CountTokensRequestFilterSensitiveLog } from "../models/models_0";
import { de_CountTokensCommand, se_CountTokensCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class CountTokensCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockFrontendService", "CountTokens", {})
    .n("BedrockRuntimeClient", "CountTokensCommand")
    .f(CountTokensRequestFilterSensitiveLog, void 0)
    .ser(se_CountTokensCommand)
    .de(de_CountTokensCommand)
    .build() {
}
