#!/bin/bash

# Script to test deployed infrastructure

set -e

STACK_NAME="RealtimeAudioChatStack"

echo "🧪 Testing deployed infrastructure..."

# Check if stack exists
if ! aws cloudformation describe-stacks --stack-name $STACK_NAME > /dev/null 2>&1; then
    echo "❌ Stack $STACK_NAME not found. Please deploy first."
    exit 1
fi

echo "✅ Stack $STACK_NAME found"

# Get stack outputs
echo "📋 Retrieving stack outputs..."
WEBSOCKET_ENDPOINT=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='WebSocketApiEndpoint'].OutputValue" --output text)
SESSIONS_TABLE=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='ConversationSessionsTableName'].OutputValue" --output text)
MESSAGES_TABLE=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='MessagesTableName'].OutputValue" --output text)

echo "  WebSocket Endpoint: $WEBSOCKET_ENDPOINT"
echo "  Sessions Table: $SESSIONS_TABLE"
echo "  Messages Table: $MESSAGES_TABLE"

# Test DynamoDB tables
echo "🗄️  Testing DynamoDB tables..."
if aws dynamodb describe-table --table-name $SESSIONS_TABLE > /dev/null 2>&1; then
    echo "  ✅ Sessions table accessible"
else
    echo "  ❌ Sessions table not accessible"
fi

if aws dynamodb describe-table --table-name $MESSAGES_TABLE > /dev/null 2>&1; then
    echo "  ✅ Messages table accessible"
else
    echo "  ❌ Messages table not accessible"
fi

# Test Lambda functions
echo "🔧 Testing Lambda functions..."
FUNCTIONS=("realtime-audio-chat-stream-processor" "realtime-audio-chat-transcription-handler" "realtime-audio-chat-ai-response-generator" "realtime-audio-chat-audio-synthesizer")

for func in "${FUNCTIONS[@]}"; do
    if aws lambda get-function --function-name $func > /dev/null 2>&1; then
        echo "  ✅ $func exists and accessible"
    else
        echo "  ❌ $func not accessible"
    fi
done

# Test WebSocket API
echo "🌐 Testing WebSocket API..."
API_ID=$(echo $WEBSOCKET_ENDPOINT | sed 's/wss:\/\///' | cut -d'.' -f1)
if aws apigatewayv2 get-api --api-id $API_ID > /dev/null 2>&1; then
    echo "  ✅ WebSocket API accessible"
else
    echo "  ❌ WebSocket API not accessible"
fi

echo ""
echo "🎉 Infrastructure test completed!"
echo ""
echo "📝 Next steps:"
echo "  1. Run './extract-config.sh' to generate frontend configuration"
echo "  2. Implement Lambda function code"
echo "  3. Test end-to-end functionality"