import { loadRestJsonErrorCode, parseJsonBody as parseBody, parseJsonErrorBody as parseErrorBody } from "@aws-sdk/core";
import { requestBuilder as rb } from "@smithy/core";
import { collectBody, decorateServiceException as __decorateServiceException, expectNonNull as __expectNonNull, expectObject as __expectObject, expectString as __expectString, map, parseRfc3339DateTimeWithOffset as __parseRfc3339DateTimeWithOffset, take, withBaseException, } from "@smithy/smithy-client";
import { ApiGatewayManagementApiServiceException as __BaseException } from "../models/ApiGatewayManagementApiServiceException";
import { ForbiddenException, GoneException, LimitExceededException, PayloadTooLargeException, } from "../models/models_0";
export const se_DeleteConnectionCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/@connections/{ConnectionId}");
    b.p("ConnectionId", () => input.ConnectionId, "{ConnectionId}", false);
    let body;
    b.m("DELETE").h(headers).b(body);
    return b.build();
};
export const se_GetConnectionCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/@connections/{ConnectionId}");
    b.p("ConnectionId", () => input.ConnectionId, "{ConnectionId}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_PostToConnectionCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {
        "content-type": "application/octet-stream",
    };
    b.bp("/@connections/{ConnectionId}");
    b.p("ConnectionId", () => input.ConnectionId, "{ConnectionId}", false);
    let body;
    if (input.Data !== undefined) {
        body = input.Data;
    }
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const de_DeleteConnectionCommand = async (output, context) => {
    if (output.statusCode !== 204 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
export const de_GetConnectionCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        ConnectedAt: [, (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)), `connectedAt`],
        Identity: [, (_) => de_Identity(_, context), `identity`],
        LastActiveAt: [, (_) => __expectNonNull(__parseRfc3339DateTimeWithOffset(_)), `lastActiveAt`],
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_PostToConnectionCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    await collectBody(output.body, context);
    return contents;
};
const de_CommandError = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseErrorBody(output.body, context),
    };
    const errorCode = loadRestJsonErrorCode(output, parsedOutput.body);
    switch (errorCode) {
        case "ForbiddenException":
        case "com.amazonaws.apigatewaymanagementapi#ForbiddenException":
            throw await de_ForbiddenExceptionRes(parsedOutput, context);
        case "GoneException":
        case "com.amazonaws.apigatewaymanagementapi#GoneException":
            throw await de_GoneExceptionRes(parsedOutput, context);
        case "LimitExceededException":
        case "com.amazonaws.apigatewaymanagementapi#LimitExceededException":
            throw await de_LimitExceededExceptionRes(parsedOutput, context);
        case "PayloadTooLargeException":
        case "com.amazonaws.apigatewaymanagementapi#PayloadTooLargeException":
            throw await de_PayloadTooLargeExceptionRes(parsedOutput, context);
        default:
            const parsedBody = parsedOutput.body;
            return throwDefaultError({
                output,
                parsedBody,
                errorCode,
            });
    }
};
const throwDefaultError = withBaseException(__BaseException);
const de_ForbiddenExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {});
    Object.assign(contents, doc);
    const exception = new ForbiddenException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_GoneExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {});
    Object.assign(contents, doc);
    const exception = new GoneException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_LimitExceededExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {});
    Object.assign(contents, doc);
    const exception = new LimitExceededException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_PayloadTooLargeExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        Message: [, __expectString, `message`],
    });
    Object.assign(contents, doc);
    const exception = new PayloadTooLargeException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_Identity = (output, context) => {
    return take(output, {
        SourceIp: [, __expectString, `sourceIp`],
        UserAgent: [, __expectString, `userAgent`],
    });
};
const deserializeMetadata = (output) => ({
    httpStatusCode: output.statusCode,
    requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
    extendedRequestId: output.headers["x-amz-id-2"],
    cfId: output.headers["x-amz-cf-id"],
});
const collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));
