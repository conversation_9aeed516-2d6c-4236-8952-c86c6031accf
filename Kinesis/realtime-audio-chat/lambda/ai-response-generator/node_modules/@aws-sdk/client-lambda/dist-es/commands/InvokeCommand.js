import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { InvocationRequestFilterSensitiveLog, InvocationResponseFilterSensitiveLog, } from "../models/models_0";
import { de_InvokeCommand, se_InvokeCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class InvokeCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "Invoke", {})
    .n("LambdaClient", "InvokeCommand")
    .f(InvocationRequestFilterSensitiveLog, InvocationResponseFilterSensitiveLog)
    .ser(se_InvokeCommand)
    .de(de_InvokeCommand)
    .build() {
}
