import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import { CreateBackupInput, CreateBackupOutput } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateBackupCommandInput extends CreateBackupInput {}
export interface CreateBackupCommandOutput
  extends CreateBackupOutput,
    __MetadataBearer {}
declare const CreateBackupCommand_base: {
  new (
    input: CreateBackupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateBackupCommandInput,
    CreateBackupCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateBackupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateBackupCommandInput,
    CreateBackupCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateBackupCommand extends CreateBackupCommand_base {
  protected static __types: {
    api: {
      input: CreateBackupInput;
      output: CreateBackupOutput;
    };
    sdk: {
      input: CreateBackupCommandInput;
      output: CreateBackupCommandOutput;
    };
  };
}
