import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_DeleteFunctionUrlConfigCommand, se_DeleteFunctionUrlConfigCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class DeleteFunctionUrlConfigCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "DeleteFunctionUrlConfig", {})
    .n("LambdaClient", "DeleteFunctionUrlConfigCommand")
    .f(void 0, void 0)
    .ser(se_DeleteFunctionUrlConfigCommand)
    .de(de_DeleteFunctionUrlConfigCommand)
    .build() {
}
