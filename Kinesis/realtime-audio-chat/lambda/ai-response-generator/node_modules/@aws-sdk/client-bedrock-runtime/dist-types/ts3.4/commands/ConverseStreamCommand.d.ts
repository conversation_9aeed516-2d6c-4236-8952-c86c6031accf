import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockRuntimeClient";
import {
  ConverseStreamRequest,
  ConverseStreamResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ConverseStreamCommandInput extends ConverseStreamRequest {}
export interface ConverseStreamCommandOutput
  extends ConverseStreamResponse,
    __MetadataBearer {}
declare const ConverseStreamCommand_base: {
  new (
    input: ConverseStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ConverseStreamCommandInput,
    ConverseStreamCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ConverseStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ConverseStreamCommandInput,
    ConverseStreamCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ConverseStreamCommand extends ConverseStreamCommand_base {
  protected static __types: {
    api: {
      input: ConverseStreamRequest;
      output: ConverseStreamResponse;
    };
    sdk: {
      input: ConverseStreamCommandInput;
      output: ConverseStreamCommandOutput;
    };
  };
}
