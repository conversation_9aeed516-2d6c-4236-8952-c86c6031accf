import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockRuntimeClient";
import {
  GetAsyncInvokeRequest,
  GetAsyncInvokeResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface GetAsyncInvokeCommandInput extends GetAsyncInvokeRequest {}
export interface GetAsyncInvokeCommandOutput
  extends GetAsyncInvokeResponse,
    __MetadataBearer {}
declare const GetAsyncInvokeCommand_base: {
  new (
    input: GetAsyncInvokeCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetAsyncInvokeCommandInput,
    GetAsyncInvokeCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetAsyncInvokeCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetAsyncInvokeCommandInput,
    GetAsyncInvokeCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetAsyncInvokeCommand extends GetAsyncInvokeCommand_base {
  protected static __types: {
    api: {
      input: GetAsyncInvokeRequest;
      output: GetAsyncInvokeResponse;
    };
    sdk: {
      input: GetAsyncInvokeCommandInput;
      output: GetAsyncInvokeCommandOutput;
    };
  };
}
