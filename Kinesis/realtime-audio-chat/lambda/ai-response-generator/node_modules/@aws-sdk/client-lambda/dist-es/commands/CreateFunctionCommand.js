import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { CreateFunctionRequestFilterSensitiveLog, FunctionConfigurationFilterSensitiveLog, } from "../models/models_0";
import { de_CreateFunctionCommand, se_CreateFunctionCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class CreateFunctionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "CreateFunction", {})
    .n("LambdaClient", "CreateFunctionCommand")
    .f(CreateFunctionRequestFilterSensitiveLog, FunctionConfigurationFilterSensitiveLog)
    .ser(se_CreateFunctionCommand)
    .de(de_CreateFunctionCommand)
    .build() {
}
