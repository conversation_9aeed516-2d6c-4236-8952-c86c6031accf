"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Reference = void 0;
const reference_1 = __importDefault(require("eslint-scope/lib/reference"));
const Reference = reference_1.default;
exports.Reference = Reference;
//# sourceMappingURL=Reference.js.map