/* Theme Toggle Styles */
.theme-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: inherit;
  backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.theme-toggle:active {
  transform: translateY(0);
}

.theme-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.5);
}

/* Size variants */
.theme-toggle-small {
  padding: 0.375rem;
  border-radius: 6px;
}

.theme-toggle-small .theme-toggle-icon {
  width: 16px;
  height: 16px;
}

.theme-toggle-small .theme-toggle-label {
  font-size: 0.75rem;
}

.theme-toggle-medium {
  padding: 0.5rem;
  border-radius: 8px;
}

.theme-toggle-medium .theme-toggle-icon {
  width: 20px;
  height: 20px;
}

.theme-toggle-medium .theme-toggle-label {
  font-size: 0.875rem;
}

.theme-toggle-large {
  padding: 0.75rem;
  border-radius: 10px;
}

.theme-toggle-large .theme-toggle-icon {
  width: 24px;
  height: 24px;
}

.theme-toggle-large .theme-toggle-label {
  font-size: 1rem;
}

/* Icon styles */
.theme-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.theme-toggle:hover .theme-toggle-icon {
  transform: rotate(15deg);
}

.theme-toggle-icon svg {
  width: 100%;
  height: 100%;
}

/* Label styles */
.theme-toggle-label {
  font-weight: 500;
  white-space: nowrap;
  opacity: 0.9;
}

/* Dark theme adjustments */
.theme-dark .theme-toggle {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

.theme-dark .theme-toggle:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Animation for theme transitions */
.theme-toggle-icon {
  animation: themeTransition 0.3s ease-in-out;
}

@keyframes themeTransition {
  0% { transform: scale(1) rotate(0deg); }
  50% { transform: scale(1.1) rotate(180deg); }
  100% { transform: scale(1) rotate(360deg); }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .theme-toggle,
  .theme-toggle-icon {
    transition: none;
    animation: none;
  }
  
  .theme-toggle:hover .theme-toggle-icon {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .theme-toggle {
    border-width: 2px;
    background: transparent;
  }
  
  .theme-toggle:hover {
    background: rgba(0, 0, 0, 0.1);
  }
  
  .theme-dark .theme-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}
