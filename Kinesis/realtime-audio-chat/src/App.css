/* CSS Variables for theming */
:root {
  --bg-primary: #f8fafc;
  --bg-secondary: #e2e8f0;
  --bg-card: rgba(255, 255, 255, 0.85);
  --bg-overlay: rgba(255, 255, 255, 0.9);
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --border-color: rgba(255, 255, 255, 0.3);
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --accent-primary: #667eea;
  --accent-secondary: #764ba2;
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-card: rgba(30, 41, 59, 0.85);
  --bg-overlay: rgba(15, 23, 42, 0.9);
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.4);
  --accent-primary: #818cf8;
  --accent-secondary: #a78bfa;
}

/* Enterprise App Styles */
.enterprise-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  position: relative;
  overflow-x: hidden;
  color: var(--text-primary);
  transition: background 0.3s ease, color 0.3s ease;
}

/* Add subtle animated background pattern */
.enterprise-app::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  animation: backgroundFloat 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

[data-theme="dark"] .enterprise-app::before {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(129, 140, 248, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(167, 139, 250, 0.08) 0%, transparent 50%);
}

.app-layout {
  display: flex;
  flex: 1;
  min-height: 0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.content-container {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Section Styles */
.section-header {
  margin-bottom: 2rem;
  text-align: center;
}

.section-header h2 {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.section-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

/* Chat Section */
.chat-section {
  max-width: 1000px;
  margin: 0 auto;
}

.chat-container {
  background: var(--bg-card);
  border-radius: 24px;
  padding: 2rem;
  box-shadow:
    0 20px 40px var(--shadow-light),
    0 1px 3px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-container:hover {
  transform: translateY(-2px);
  box-shadow:
    0 25px 50px var(--shadow-medium),
    0 2px 6px rgba(0, 0, 0, 0.08);
}

.chat-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Analytics Section */
.analytics-section {
  max-width: 1200px;
  margin: 0 auto;
}

.analytics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.analytics-card {
  background: var(--bg-card);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px var(--shadow-light);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.analytics-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px var(--shadow-medium);
}

/* History Section */
.history-section {
  max-width: 800px;
  margin: 0 auto;
}

.history-placeholder {
  background: var(--bg-card);
  border-radius: 20px;
  padding: 4rem 2rem;
  text-align: center;
  box-shadow: 0 20px 40px var(--shadow-light);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
}

.placeholder-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.placeholder-icon svg {
  width: 40px;
  height: 40px;
  color: white;
}

.history-placeholder h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.history-placeholder p {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
  line-height: 1.6;
}

/* Settings Section */
.settings-section {
  max-width: 1000px;
  margin: 0 auto;
}

.settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.settings-card {
  background: var(--bg-card);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px var(--shadow-light);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.settings-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.settings-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px var(--shadow-medium);
}

.settings-card:hover::before {
  left: 100%;
}

.settings-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--border-color);
  position: relative;
}

.settings-card h3::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  transition: width 0.3s ease;
}

.settings-card:hover h3::after {
  width: 100%;
}

.setting-item {
  margin-bottom: 1.5rem;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.setting-item select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-card);
  font-size: 0.875rem;
  color: var(--text-primary);
  transition: all 0.3s ease;
  cursor: pointer;
}

.setting-item select:hover {
  border-color: var(--accent-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-light);
}

.setting-item select:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.setting-item input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: var(--accent-primary);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.setting-item input[type="checkbox"]:hover {
  transform: scale(1.1);
}

.setting-item input[type="checkbox"]:checked {
  animation: checkboxPulse 0.3s ease;
}

@keyframes checkboxPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* Animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes backgroundFloat {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.5;
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
    opacity: 0.8;
  }
  66% {
    transform: translate(-20px, 20px) rotate(240deg);
    opacity: 0.3;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Add entrance animations */
.section-header {
  animation: fadeInUp 0.6s ease-out;
}

.chat-container,
.analytics-card,
.settings-card,
.history-placeholder {
  animation: fadeInUp 0.8s ease-out;
}

.analytics-card:nth-child(2) {
  animation-delay: 0.1s;
}

.settings-card:nth-child(2) {
  animation-delay: 0.1s;
}

/* Floating theme toggle */
.floating-theme-toggle {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  animation: slideInRight 0.6s ease-out 0.5s both;
}

/* Responsive Design - Mobile First Approach */

/* Extra small devices (phones, 576px and down) */
@media (max-width: 575.98px) {
  .content-container {
    padding: 1rem;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .section-header p {
    font-size: 0.9rem;
  }

  .chat-container,
  .history-placeholder,
  .settings-card {
    padding: 1rem;
    border-radius: 16px;
  }

  .analytics-grid,
  .settings-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .placeholder-icon {
    width: 50px;
    height: 50px;
  }

  .placeholder-icon svg {
    width: 25px;
    height: 25px;
  }

  .floating-theme-toggle {
    bottom: 1rem;
    right: 1rem;
  }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .content-container {
    padding: 1.25rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }

  .chat-container,
  .history-placeholder {
    padding: 1.5rem;
    border-radius: 18px;
  }

  .settings-card {
    padding: 1.5rem;
  }

  .analytics-grid,
  .settings-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .placeholder-icon {
    width: 60px;
    height: 60px;
  }

  .placeholder-icon svg {
    width: 30px;
    height: 30px;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .content-container {
    padding: 1.5rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .analytics-grid,
  .settings-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .chat-container,
  .history-placeholder {
    padding: 2rem;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .analytics-grid,
  .settings-grid {
    grid-template-columns: 1fr 1fr;
    gap: 1.75rem;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .content-container {
    padding: 2.5rem;
  }

  .section-header h2 {
    font-size: 2.5rem;
  }

  .analytics-grid,
  .settings-grid {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

/* Ultra-wide screens */
@media (min-width: 1400px) {
  .analytics-grid,
  .settings-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 600px) {
  .content-container {
    padding: 1rem 2rem;
  }

  .section-header {
    margin-bottom: 1rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }

  .chat-container,
  .history-placeholder {
    padding: 1.5rem;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chat-container::before {
    background-size: 100% 200%;
  }
}