import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { EventStreamSerdeContext as __EventStreamSerdeContext, SerdeContext as __SerdeContext } from "@smithy/types";
import { ApplyGuardrailCommandInput, ApplyGuardrailCommandOutput } from "../commands/ApplyGuardrailCommand";
import { ConverseCommandInput, ConverseCommandOutput } from "../commands/ConverseCommand";
import { ConverseStreamCommandInput, ConverseStreamCommandOutput } from "../commands/ConverseStreamCommand";
import { CountTokensCommandInput, CountTokensCommandOutput } from "../commands/CountTokensCommand";
import { GetAsyncInvokeCommandInput, GetAsyncInvokeCommandOutput } from "../commands/GetAsyncInvokeCommand";
import { InvokeModelCommandInput, InvokeModelCommandOutput } from "../commands/InvokeModelCommand";
import { InvokeModelWithBidirectionalStreamCommandInput, InvokeModelWithBidirectionalStreamCommandOutput } from "../commands/InvokeModelWithBidirectionalStreamCommand";
import { InvokeModelWithResponseStreamCommandInput, InvokeModelWithResponseStreamCommandOutput } from "../commands/InvokeModelWithResponseStreamCommand";
import { ListAsyncInvokesCommandInput, ListAsyncInvokesCommandOutput } from "../commands/ListAsyncInvokesCommand";
import { StartAsyncInvokeCommandInput, StartAsyncInvokeCommandOutput } from "../commands/StartAsyncInvokeCommand";
/**
 * serializeAws_restJson1ApplyGuardrailCommand
 */
export declare const se_ApplyGuardrailCommand: (input: ApplyGuardrailCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ConverseCommand
 */
export declare const se_ConverseCommand: (input: ConverseCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ConverseStreamCommand
 */
export declare const se_ConverseStreamCommand: (input: ConverseStreamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CountTokensCommand
 */
export declare const se_CountTokensCommand: (input: CountTokensCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetAsyncInvokeCommand
 */
export declare const se_GetAsyncInvokeCommand: (input: GetAsyncInvokeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1InvokeModelCommand
 */
export declare const se_InvokeModelCommand: (input: InvokeModelCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1InvokeModelWithBidirectionalStreamCommand
 */
export declare const se_InvokeModelWithBidirectionalStreamCommand: (input: InvokeModelWithBidirectionalStreamCommandInput, context: __SerdeContext & __EventStreamSerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1InvokeModelWithResponseStreamCommand
 */
export declare const se_InvokeModelWithResponseStreamCommand: (input: InvokeModelWithResponseStreamCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListAsyncInvokesCommand
 */
export declare const se_ListAsyncInvokesCommand: (input: ListAsyncInvokesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1StartAsyncInvokeCommand
 */
export declare const se_StartAsyncInvokeCommand: (input: StartAsyncInvokeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_restJson1ApplyGuardrailCommand
 */
export declare const de_ApplyGuardrailCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ApplyGuardrailCommandOutput>;
/**
 * deserializeAws_restJson1ConverseCommand
 */
export declare const de_ConverseCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ConverseCommandOutput>;
/**
 * deserializeAws_restJson1ConverseStreamCommand
 */
export declare const de_ConverseStreamCommand: (output: __HttpResponse, context: __SerdeContext & __EventStreamSerdeContext) => Promise<ConverseStreamCommandOutput>;
/**
 * deserializeAws_restJson1CountTokensCommand
 */
export declare const de_CountTokensCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CountTokensCommandOutput>;
/**
 * deserializeAws_restJson1GetAsyncInvokeCommand
 */
export declare const de_GetAsyncInvokeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetAsyncInvokeCommandOutput>;
/**
 * deserializeAws_restJson1InvokeModelCommand
 */
export declare const de_InvokeModelCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<InvokeModelCommandOutput>;
/**
 * deserializeAws_restJson1InvokeModelWithBidirectionalStreamCommand
 */
export declare const de_InvokeModelWithBidirectionalStreamCommand: (output: __HttpResponse, context: __SerdeContext & __EventStreamSerdeContext) => Promise<InvokeModelWithBidirectionalStreamCommandOutput>;
/**
 * deserializeAws_restJson1InvokeModelWithResponseStreamCommand
 */
export declare const de_InvokeModelWithResponseStreamCommand: (output: __HttpResponse, context: __SerdeContext & __EventStreamSerdeContext) => Promise<InvokeModelWithResponseStreamCommandOutput>;
/**
 * deserializeAws_restJson1ListAsyncInvokesCommand
 */
export declare const de_ListAsyncInvokesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListAsyncInvokesCommandOutput>;
/**
 * deserializeAws_restJson1StartAsyncInvokeCommand
 */
export declare const de_StartAsyncInvokeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartAsyncInvokeCommandOutput>;
