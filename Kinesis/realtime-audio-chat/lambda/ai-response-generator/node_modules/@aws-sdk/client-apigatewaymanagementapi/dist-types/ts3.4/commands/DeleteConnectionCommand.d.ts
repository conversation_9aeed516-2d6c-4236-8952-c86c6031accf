import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ApiGatewayManagementApiClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ApiGatewayManagementApiClient";
import { DeleteConnectionRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteConnectionCommandInput extends DeleteConnectionRequest {}
export interface DeleteConnectionCommandOutput extends __MetadataBearer {}
declare const DeleteConnectionCommand_base: {
  new (
    input: DeleteConnectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteConnectionCommandInput,
    DeleteConnectionCommandOutput,
    ApiGatewayManagementApiClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteConnectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteConnectionCommandInput,
    DeleteConnectionCommandOutput,
    ApiGatewayManagementApiClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteConnectionCommand extends DeleteConnectionCommand_base {
  protected static __types: {
    api: {
      input: DeleteConnectionRequest;
      output: {};
    };
    sdk: {
      input: DeleteConnectionCommandInput;
      output: DeleteConnectionCommandOutput;
    };
  };
}
