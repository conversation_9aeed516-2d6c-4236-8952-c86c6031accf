import { getEventStreamPlugin } from "@aws-sdk/middleware-eventstream";
import { getWebSocketPlugin } from "@aws-sdk/middleware-websocket";
import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog, InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog, } from "../models/models_0";
import { de_InvokeModelWithBidirectionalStreamCommand, se_InvokeModelWithBidirectionalStreamCommand, } from "../protocols/Aws_restJson1";
export { $Command };
export class InvokeModelWithBidirectionalStreamCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
        getEventStreamPlugin(config),
        getWebSocketPlugin(config, {
            headerPrefix: "x-amz-bedrock-",
        }),
    ];
})
    .s("AmazonBedrockFrontendService", "InvokeModelWithBidirectionalStream", {
    eventStream: {
        input: true,
        output: true,
    },
})
    .n("BedrockRuntimeClient", "InvokeModelWithBidirectionalStreamCommand")
    .f(InvokeModelWithBidirectionalStreamRequestFilterSensitiveLog, InvokeModelWithBidirectionalStreamResponseFilterSensitiveLog)
    .ser(se_InvokeModelWithBidirectionalStreamCommand)
    .de(de_InvokeModelWithBidirectionalStreamCommand)
    .build() {
}
