"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  ApiGatewayManagementApi: () => ApiGatewayManagementApi,
  ApiGatewayManagementApiClient: () => ApiGatewayManagementApiClient,
  ApiGatewayManagementApiServiceException: () => ApiGatewayManagementApiServiceException,
  DeleteConnectionCommand: () => DeleteConnectionCommand,
  ForbiddenException: () => ForbiddenException,
  GetConnectionCommand: () => GetConnectionCommand,
  GoneException: () => GoneException,
  LimitExceededException: () => LimitExceededException,
  PayloadTooLargeException: () => PayloadTooLargeException,
  PostToConnectionCommand: () => PostToConnectionCommand,
  __Client: () => import_smithy_client.Client
});
module.exports = __toCommonJS(index_exports);

// src/ApiGatewayManagementApiClient.ts
var import_middleware_host_header = require("@aws-sdk/middleware-host-header");
var import_middleware_logger = require("@aws-sdk/middleware-logger");
var import_middleware_recursion_detection = require("@aws-sdk/middleware-recursion-detection");
var import_middleware_user_agent = require("@aws-sdk/middleware-user-agent");
var import_config_resolver = require("@smithy/config-resolver");
var import_core = require("@smithy/core");
var import_middleware_content_length = require("@smithy/middleware-content-length");
var import_middleware_endpoint = require("@smithy/middleware-endpoint");
var import_middleware_retry = require("@smithy/middleware-retry");

var import_httpAuthSchemeProvider = require("./auth/httpAuthSchemeProvider");

// src/endpoint/EndpointParameters.ts
var resolveClientEndpointParameters = /* @__PURE__ */ __name((options) => {
  return Object.assign(options, {
    useDualstackEndpoint: options.useDualstackEndpoint ?? false,
    useFipsEndpoint: options.useFipsEndpoint ?? false,
    defaultSigningName: "execute-api"
  });
}, "resolveClientEndpointParameters");
var commonParams = {
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};

// src/ApiGatewayManagementApiClient.ts
var import_runtimeConfig = require("././runtimeConfig");

// src/runtimeExtensions.ts
var import_region_config_resolver = require("@aws-sdk/region-config-resolver");
var import_protocol_http = require("@smithy/protocol-http");
var import_smithy_client = require("@smithy/smithy-client");

// src/auth/httpAuthExtensionConfiguration.ts
var getHttpAuthExtensionConfiguration = /* @__PURE__ */ __name((runtimeConfig) => {
  const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;
  let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;
  let _credentials = runtimeConfig.credentials;
  return {
    setHttpAuthScheme(httpAuthScheme) {
      const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);
      if (index === -1) {
        _httpAuthSchemes.push(httpAuthScheme);
      } else {
        _httpAuthSchemes.splice(index, 1, httpAuthScheme);
      }
    },
    httpAuthSchemes() {
      return _httpAuthSchemes;
    },
    setHttpAuthSchemeProvider(httpAuthSchemeProvider) {
      _httpAuthSchemeProvider = httpAuthSchemeProvider;
    },
    httpAuthSchemeProvider() {
      return _httpAuthSchemeProvider;
    },
    setCredentials(credentials) {
      _credentials = credentials;
    },
    credentials() {
      return _credentials;
    }
  };
}, "getHttpAuthExtensionConfiguration");
var resolveHttpAuthRuntimeConfig = /* @__PURE__ */ __name((config) => {
  return {
    httpAuthSchemes: config.httpAuthSchemes(),
    httpAuthSchemeProvider: config.httpAuthSchemeProvider(),
    credentials: config.credentials()
  };
}, "resolveHttpAuthRuntimeConfig");

// src/runtimeExtensions.ts
var resolveRuntimeExtensions = /* @__PURE__ */ __name((runtimeConfig, extensions) => {
  const extensionConfiguration = Object.assign(
    (0, import_region_config_resolver.getAwsRegionExtensionConfiguration)(runtimeConfig),
    (0, import_smithy_client.getDefaultExtensionConfiguration)(runtimeConfig),
    (0, import_protocol_http.getHttpHandlerExtensionConfiguration)(runtimeConfig),
    getHttpAuthExtensionConfiguration(runtimeConfig)
  );
  extensions.forEach((extension) => extension.configure(extensionConfiguration));
  return Object.assign(
    runtimeConfig,
    (0, import_region_config_resolver.resolveAwsRegionExtensionConfiguration)(extensionConfiguration),
    (0, import_smithy_client.resolveDefaultRuntimeConfig)(extensionConfiguration),
    (0, import_protocol_http.resolveHttpHandlerRuntimeConfig)(extensionConfiguration),
    resolveHttpAuthRuntimeConfig(extensionConfiguration)
  );
}, "resolveRuntimeExtensions");

// src/ApiGatewayManagementApiClient.ts
var ApiGatewayManagementApiClient = class extends import_smithy_client.Client {
  static {
    __name(this, "ApiGatewayManagementApiClient");
  }
  /**
   * The resolved configuration of ApiGatewayManagementApiClient class. This is resolved and normalized from the {@link ApiGatewayManagementApiClientConfig | constructor configuration interface}.
   */
  config;
  constructor(...[configuration]) {
    const _config_0 = (0, import_runtimeConfig.getRuntimeConfig)(configuration || {});
    super(_config_0);
    this.initConfig = _config_0;
    const _config_1 = resolveClientEndpointParameters(_config_0);
    const _config_2 = (0, import_middleware_user_agent.resolveUserAgentConfig)(_config_1);
    const _config_3 = (0, import_middleware_retry.resolveRetryConfig)(_config_2);
    const _config_4 = (0, import_config_resolver.resolveRegionConfig)(_config_3);
    const _config_5 = (0, import_middleware_host_header.resolveHostHeaderConfig)(_config_4);
    const _config_6 = (0, import_middleware_endpoint.resolveEndpointConfig)(_config_5);
    const _config_7 = (0, import_httpAuthSchemeProvider.resolveHttpAuthSchemeConfig)(_config_6);
    const _config_8 = resolveRuntimeExtensions(_config_7, configuration?.extensions || []);
    this.config = _config_8;
    this.middlewareStack.use((0, import_middleware_user_agent.getUserAgentPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_retry.getRetryPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_content_length.getContentLengthPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_host_header.getHostHeaderPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_logger.getLoggerPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_recursion_detection.getRecursionDetectionPlugin)(this.config));
    this.middlewareStack.use(
      (0, import_core.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {
        httpAuthSchemeParametersProvider: import_httpAuthSchemeProvider.defaultApiGatewayManagementApiHttpAuthSchemeParametersProvider,
        identityProviderConfigProvider: /* @__PURE__ */ __name(async (config) => new import_core.DefaultIdentityProviderConfig({
          "aws.auth#sigv4": config.credentials
        }), "identityProviderConfigProvider")
      })
    );
    this.middlewareStack.use((0, import_core.getHttpSigningPlugin)(this.config));
  }
  /**
   * Destroy underlying resources, like sockets. It's usually not necessary to do this.
   * However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
   * Otherwise, sockets might stay open for quite a long time before the server terminates them.
   */
  destroy() {
    super.destroy();
  }
};

// src/ApiGatewayManagementApi.ts


// src/commands/DeleteConnectionCommand.ts

var import_middleware_serde = require("@smithy/middleware-serde");


// src/protocols/Aws_restJson1.ts
var import_core2 = require("@aws-sdk/core");



// src/models/ApiGatewayManagementApiServiceException.ts

var ApiGatewayManagementApiServiceException = class _ApiGatewayManagementApiServiceException extends import_smithy_client.ServiceException {
  static {
    __name(this, "ApiGatewayManagementApiServiceException");
  }
  /**
   * @internal
   */
  constructor(options) {
    super(options);
    Object.setPrototypeOf(this, _ApiGatewayManagementApiServiceException.prototype);
  }
};

// src/models/models_0.ts
var ForbiddenException = class _ForbiddenException extends ApiGatewayManagementApiServiceException {
  static {
    __name(this, "ForbiddenException");
  }
  name = "ForbiddenException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ForbiddenException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ForbiddenException.prototype);
  }
};
var GoneException = class _GoneException extends ApiGatewayManagementApiServiceException {
  static {
    __name(this, "GoneException");
  }
  name = "GoneException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "GoneException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _GoneException.prototype);
  }
};
var LimitExceededException = class _LimitExceededException extends ApiGatewayManagementApiServiceException {
  static {
    __name(this, "LimitExceededException");
  }
  name = "LimitExceededException";
  $fault = "client";
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "LimitExceededException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _LimitExceededException.prototype);
  }
};
var PayloadTooLargeException = class _PayloadTooLargeException extends ApiGatewayManagementApiServiceException {
  static {
    __name(this, "PayloadTooLargeException");
  }
  name = "PayloadTooLargeException";
  $fault = "client";
  Message;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "PayloadTooLargeException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _PayloadTooLargeException.prototype);
    this.Message = opts.Message;
  }
};

// src/protocols/Aws_restJson1.ts
var se_DeleteConnectionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/@connections/{ConnectionId}");
  b.p("ConnectionId", () => input.ConnectionId, "{ConnectionId}", false);
  let body;
  b.m("DELETE").h(headers).b(body);
  return b.build();
}, "se_DeleteConnectionCommand");
var se_GetConnectionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/@connections/{ConnectionId}");
  b.p("ConnectionId", () => input.ConnectionId, "{ConnectionId}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetConnectionCommand");
var se_PostToConnectionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {
    "content-type": "application/octet-stream"
  };
  b.bp("/@connections/{ConnectionId}");
  b.p("ConnectionId", () => input.ConnectionId, "{ConnectionId}", false);
  let body;
  if (input.Data !== void 0) {
    body = input.Data;
  }
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_PostToConnectionCommand");
var de_DeleteConnectionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 204 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_DeleteConnectionCommand");
var de_GetConnectionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    ConnectedAt: [, (_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), `connectedAt`],
    Identity: [, (_) => de_Identity(_, context), `identity`],
    LastActiveAt: [, (_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseRfc3339DateTimeWithOffset)(_)), `lastActiveAt`]
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetConnectionCommand");
var de_PostToConnectionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  await (0, import_smithy_client.collectBody)(output.body, context);
  return contents;
}, "de_PostToConnectionCommand");
var de_CommandError = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonErrorBody)(output.body, context)
  };
  const errorCode = (0, import_core2.loadRestJsonErrorCode)(output, parsedOutput.body);
  switch (errorCode) {
    case "ForbiddenException":
    case "com.amazonaws.apigatewaymanagementapi#ForbiddenException":
      throw await de_ForbiddenExceptionRes(parsedOutput, context);
    case "GoneException":
    case "com.amazonaws.apigatewaymanagementapi#GoneException":
      throw await de_GoneExceptionRes(parsedOutput, context);
    case "LimitExceededException":
    case "com.amazonaws.apigatewaymanagementapi#LimitExceededException":
      throw await de_LimitExceededExceptionRes(parsedOutput, context);
    case "PayloadTooLargeException":
    case "com.amazonaws.apigatewaymanagementapi#PayloadTooLargeException":
      throw await de_PayloadTooLargeExceptionRes(parsedOutput, context);
    default:
      const parsedBody = parsedOutput.body;
      return throwDefaultError({
        output,
        parsedBody,
        errorCode
      });
  }
}, "de_CommandError");
var throwDefaultError = (0, import_smithy_client.withBaseException)(ApiGatewayManagementApiServiceException);
var de_ForbiddenExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {});
  Object.assign(contents, doc);
  const exception = new ForbiddenException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ForbiddenExceptionRes");
var de_GoneExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {});
  Object.assign(contents, doc);
  const exception = new GoneException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_GoneExceptionRes");
var de_LimitExceededExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {});
  Object.assign(contents, doc);
  const exception = new LimitExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_LimitExceededExceptionRes");
var de_PayloadTooLargeExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    Message: [, import_smithy_client.expectString, `message`]
  });
  Object.assign(contents, doc);
  const exception = new PayloadTooLargeException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_PayloadTooLargeExceptionRes");
var de_Identity = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    SourceIp: [, import_smithy_client.expectString, `sourceIp`],
    UserAgent: [, import_smithy_client.expectString, `userAgent`]
  });
}, "de_Identity");
var deserializeMetadata = /* @__PURE__ */ __name((output) => ({
  httpStatusCode: output.statusCode,
  requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
  extendedRequestId: output.headers["x-amz-id-2"],
  cfId: output.headers["x-amz-cf-id"]
}), "deserializeMetadata");

// src/commands/DeleteConnectionCommand.ts
var DeleteConnectionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("ApiGatewayManagementApi", "DeleteConnection", {}).n("ApiGatewayManagementApiClient", "DeleteConnectionCommand").f(void 0, void 0).ser(se_DeleteConnectionCommand).de(de_DeleteConnectionCommand).build() {
  static {
    __name(this, "DeleteConnectionCommand");
  }
};

// src/commands/GetConnectionCommand.ts



var GetConnectionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("ApiGatewayManagementApi", "GetConnection", {}).n("ApiGatewayManagementApiClient", "GetConnectionCommand").f(void 0, void 0).ser(se_GetConnectionCommand).de(de_GetConnectionCommand).build() {
  static {
    __name(this, "GetConnectionCommand");
  }
};

// src/commands/PostToConnectionCommand.ts



var PostToConnectionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("ApiGatewayManagementApi", "PostToConnection", {}).n("ApiGatewayManagementApiClient", "PostToConnectionCommand").f(void 0, void 0).ser(se_PostToConnectionCommand).de(de_PostToConnectionCommand).build() {
  static {
    __name(this, "PostToConnectionCommand");
  }
};

// src/ApiGatewayManagementApi.ts
var commands = {
  DeleteConnectionCommand,
  GetConnectionCommand,
  PostToConnectionCommand
};
var ApiGatewayManagementApi = class extends ApiGatewayManagementApiClient {
  static {
    __name(this, "ApiGatewayManagementApi");
  }
};
(0, import_smithy_client.createAggregatedClient)(commands, ApiGatewayManagementApi);
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  ApiGatewayManagementApiServiceException,
  __Client,
  ApiGatewayManagementApiClient,
  ApiGatewayManagementApi,
  $Command,
  DeleteConnectionCommand,
  GetConnectionCommand,
  PostToConnectionCommand,
  ForbiddenException,
  GoneException,
  LimitExceededException,
  PayloadTooLargeException
});

