import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { PublishLayerVersionRequestFilterSensitiveLog, } from "../models/models_0";
import { de_PublishLayerVersionCommand, se_PublishLayerVersionCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class PublishLayerVersionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "PublishLayerVersion", {})
    .n("LambdaClient", "PublishLayerVersionCommand")
    .f(PublishLayerVersionRequestFilterSensitiveLog, void 0)
    .ser(se_PublishLayerVersionCommand)
    .de(de_PublishLayerVersionCommand)
    .build() {
}
