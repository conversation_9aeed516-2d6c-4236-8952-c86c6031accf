#!/bin/bash

# Deployment script for Realtime Audio Chat Infrastructure

set -e

echo "🚀 Starting deployment of Realtime Audio Chat Infrastructure..."

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

# Build the CDK project
echo "📦 Building CDK project..."
npm run build

# Bootstrap CDK if needed (only needs to be done once per account/region)
echo "🔧 Bootstrapping CDK (if needed)..."
npx cdk bootstrap

# Deploy the stack
echo "🚀 Deploying infrastructure stack..."
npx cdk deploy --require-approval never

echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Stack outputs:"
npx cdk list --long

echo ""
echo "🔍 To view the deployed resources:"
echo "  - DynamoDB Tables: Check AWS Console > DynamoDB"
echo "  - Lambda Functions: Check AWS Console > Lambda"
echo "  - WebSocket API: Check AWS Console > API Gateway"
echo ""
echo "📝 Next steps:"
echo "  1. Update your frontend configuration with the WebSocket endpoint"
echo "  2. Implement the Lambda function code for each service"
echo "  3. Test the infrastructure with sample data"