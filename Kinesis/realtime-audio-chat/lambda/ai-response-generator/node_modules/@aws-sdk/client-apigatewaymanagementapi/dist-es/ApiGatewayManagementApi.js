import { createAggregatedClient } from "@smithy/smithy-client";
import { ApiGatewayManagementApiClient } from "./ApiGatewayManagementApiClient";
import { DeleteConnectionCommand, } from "./commands/DeleteConnectionCommand";
import { GetConnectionCommand, } from "./commands/GetConnectionCommand";
import { PostToConnectionCommand, } from "./commands/PostToConnectionCommand";
const commands = {
    DeleteConnectionCommand,
    GetConnectionCommand,
    PostToConnectionCommand,
};
export class ApiGatewayManagementApi extends ApiGatewayManagementApiClient {
}
createAggregatedClient(commands, ApiGatewayManagementApi);
