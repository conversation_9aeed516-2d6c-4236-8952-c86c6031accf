import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_UpdateEventSourceMappingCommand, se_UpdateEventSourceMappingCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class UpdateEventSourceMappingCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "UpdateEventSourceMapping", {})
    .n("LambdaClient", "UpdateEventSourceMappingCommand")
    .f(void 0, void 0)
    .ser(se_UpdateEventSourceMappingCommand)
    .de(de_UpdateEventSourceMappingCommand)
    .build() {
}
