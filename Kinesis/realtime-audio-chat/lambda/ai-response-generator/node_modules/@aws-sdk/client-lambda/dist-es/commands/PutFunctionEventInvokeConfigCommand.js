import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_PutFunctionEventInvokeConfigCommand, se_PutFunctionEventInvokeConfigCommand, } from "../protocols/Aws_restJson1";
export { $Command };
export class PutFunctionEventInvokeConfigCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "PutFunctionEventInvokeConfig", {})
    .n("LambdaClient", "PutFunctionEventInvokeConfigCommand")
    .f(void 0, void 0)
    .ser(se_PutFunctionEventInvokeConfigCommand)
    .de(de_PutFunctionEventInvokeConfigCommand)
    .build() {
}
