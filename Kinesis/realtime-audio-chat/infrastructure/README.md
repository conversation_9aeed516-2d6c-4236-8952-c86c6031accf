# Realtime Audio Chat Infrastructure

This directory contains the AWS CDK infrastructure code for the realtime audio chat application.

## Architecture Overview

The infrastructure includes:

- **DynamoDB Tables**: For storing conversation sessions and messages
- **Lambda Functions**: For processing audio streams, transcription, AI responses, and audio synthesis
- **API Gateway WebSocket**: For real-time communication with the frontend
- **IAM Roles**: With appropriate permissions for all AWS services

## Prerequisites

1. **AWS CLI**: Configured with appropriate credentials
2. **Node.js**: Version 18 or higher
3. **AWS CDK**: Installed globally (`npm install -g aws-cdk`)

## Deployment

### 1. Install Dependencies

```bash
npm install
```

### 2. Deploy Infrastructure

```bash
./deploy.sh
```

This script will:
- Build the CDK project
- Bootstrap CDK (if needed)
- Deploy the infrastructure stack
- Display the stack outputs

### 3. Extract Configuration

After deployment, extract the configuration for the frontend:

```bash
./extract-config.sh
```

This will create `../src/config/aws-config.json` with all the necessary endpoints and resource names.

## Infrastructure Components

### DynamoDB Tables

1. **realtime-audio-chat-sessions**
   - Partition Key: `sessionId` (String)
   - Sort Key: `userId` (String)
   - Stores conversation session metadata

2. **realtime-audio-chat-messages**
   - Partition Key: `sessionId` (String)
   - Sort Key: `messageId` (String)
   - Global Secondary Index: `TimestampIndex` (sessionId, timestamp)
   - Stores individual messages and transcripts

### Lambda Functions

1. **realtime-audio-chat-stream-processor**
   - Handles Kinesis Video Stream events
   - Coordinates the processing pipeline
   - Manages WebSocket connections

2. **realtime-audio-chat-transcription-handler**
   - Processes audio through AWS Transcribe
   - Handles streaming transcription results

3. **realtime-audio-chat-ai-response-generator**
   - Generates AI responses using AWS Bedrock
   - Manages conversation context

4. **realtime-audio-chat-audio-synthesizer**
   - Converts text to speech using Amazon Polly
   - Handles audio format optimization

### WebSocket API

- **Endpoint**: Provided in stack outputs
- **Routes**: `$connect`, `$disconnect`, `$default`
- **Stage**: `prod` with auto-deployment enabled

## IAM Permissions

The Lambda execution role includes permissions for:

- **DynamoDB**: Read/write access to conversation tables
- **Kinesis Video Streams**: Access to the specified stream
- **AWS Transcribe**: Streaming transcription permissions
- **AWS Bedrock**: Model invocation permissions
- **Amazon Polly**: Speech synthesis permissions
- **API Gateway**: WebSocket connection management

## Environment Variables

Each Lambda function receives:

- `SESSIONS_TABLE`: DynamoDB sessions table name
- `MESSAGES_TABLE`: DynamoDB messages table name
- `STREAM_ARN`: Kinesis Video Stream ARN (for stream processor)
- `BEDROCK_MODEL_ID`: Bedrock model identifier (for AI generator)

## Monitoring and Logging

- **CloudWatch Logs**: All Lambda functions have log groups with 7-day retention
- **CloudWatch Metrics**: Standard Lambda and DynamoDB metrics available
- **Error Handling**: Comprehensive error handling in all components

## Cost Optimization

- **DynamoDB**: Pay-per-request billing mode
- **Lambda**: Optimized timeout settings (5 minutes max)
- **Log Retention**: 7 days to minimize storage costs

## Security

- **IAM**: Least privilege access for all resources
- **VPC**: Can be configured for additional network isolation
- **Encryption**: DynamoDB encryption at rest enabled
- **Point-in-Time Recovery**: Enabled for DynamoDB tables

## Cleanup

To remove all infrastructure:

```bash
npx cdk destroy
```

## Troubleshooting

### Common Issues

1. **Bootstrap Required**: Run `npx cdk bootstrap` if deployment fails
2. **Permissions**: Ensure AWS credentials have sufficient permissions
3. **Region**: Verify you're deploying to the correct region (us-east-1)

### Logs

Check CloudWatch logs for each Lambda function:
- `/aws/lambda/realtime-audio-chat-stream-processor`
- `/aws/lambda/realtime-audio-chat-transcription-handler`
- `/aws/lambda/realtime-audio-chat-ai-response-generator`
- `/aws/lambda/realtime-audio-chat-audio-synthesizer`

## Next Steps

After deployment:

1. Update frontend configuration with WebSocket endpoint
2. Implement Lambda function code for each service
3. Test the infrastructure with sample data
4. Configure monitoring and alerting
5. Set up CI/CD pipeline for automated deployments