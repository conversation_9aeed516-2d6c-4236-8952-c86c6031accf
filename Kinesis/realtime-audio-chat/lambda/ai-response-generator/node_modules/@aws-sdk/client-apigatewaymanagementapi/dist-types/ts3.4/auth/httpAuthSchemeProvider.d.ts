import {
  AwsSdkSigV4AuthInputConfig,
  AwsSdkSigV4AuthResolvedConfig,
  AwsSdkSigV4PreviouslyResolved,
} from "@aws-sdk/core";
import {
  HandlerExecutionContext,
  HttpAuthScheme,
  HttpAuthSchemeParameters,
  HttpAuthSchemeParametersProvider,
  HttpAuthSchemeProvider,
  Provider,
} from "@smithy/types";
import { ApiGatewayManagementApiClientResolvedConfig } from "../ApiGatewayManagementApiClient";
export interface ApiGatewayManagementApiHttpAuthSchemeParameters
  extends HttpAuthSchemeParameters {
  region?: string;
}
export interface ApiGatewayManagementApiHttpAuthSchemeParametersProvider
  extends HttpAuthSchemeParametersProvider<
    ApiGatewayManagementApiClientResolvedConfig,
    HandlerExecutionContext,
    ApiGatewayManagementApiHttpAuthSchemeParameters,
    object
  > {}
export declare const defaultApiGatewayManagementApiHttpAuthSchemeParametersProvider: (
  config: ApiGatewayManagementApiClientResolvedConfig,
  context: HandlerExecutionContext,
  input: object
) => Promise<ApiGatewayManagementApiHttpAuthSchemeParameters>;
export interface ApiGatewayManagementApiHttpAuthSchemeProvider
  extends HttpAuthSchemeProvider<ApiGatewayManagementApiHttpAuthSchemeParameters> {}
export declare const defaultApiGatewayManagementApiHttpAuthSchemeProvider: ApiGatewayManagementApiHttpAuthSchemeProvider;
export interface HttpAuthSchemeInputConfig extends AwsSdkSigV4AuthInputConfig {
  authSchemePreference?: string[] | Provider<string[]>;
  httpAuthSchemes?: HttpAuthScheme[];
  httpAuthSchemeProvider?: ApiGatewayManagementApiHttpAuthSchemeProvider;
}
export interface HttpAuthSchemeResolvedConfig
  extends AwsSdkSigV4AuthResolvedConfig {
  readonly authSchemePreference: Provider<string[]>;
  readonly httpAuthSchemes: HttpAuthScheme[];
  readonly httpAuthSchemeProvider: ApiGatewayManagementApiHttpAuthSchemeProvider;
}
export declare const resolveHttpAuthSchemeConfig: <T>(
  config: T & HttpAuthSchemeInputConfig & AwsSdkSigV4PreviouslyResolved
) => T & HttpAuthSchemeResolvedConfig;
