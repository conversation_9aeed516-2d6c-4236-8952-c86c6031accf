import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import {
  DeleteConnectionCommandInput,
  DeleteConnectionCommandOutput,
} from "../commands/DeleteConnectionCommand";
import {
  GetConnectionCommandInput,
  GetConnectionCommandOutput,
} from "../commands/GetConnectionCommand";
import {
  PostToConnectionCommandInput,
  PostToConnectionCommandOutput,
} from "../commands/PostToConnectionCommand";
export declare const se_DeleteConnectionCommand: (
  input: DeleteConnectionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetConnectionCommand: (
  input: GetConnectionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PostToConnectionCommand: (
  input: PostToConnectionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_DeleteConnectionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteConnectionCommandOutput>;
export declare const de_GetConnectionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetConnectionCommandOutput>;
export declare const de_PostToConnectionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PostToConnectionCommandOutput>;
