import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockRuntimeClient";
import {
  ApplyGuardrailRequest,
  ApplyGuardrailResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ApplyGuardrailCommandInput extends ApplyGuardrailRequest {}
export interface ApplyGuardrailCommandOutput
  extends ApplyGuardrailResponse,
    __MetadataBearer {}
declare const ApplyGuardrailCommand_base: {
  new (
    input: ApplyGuardrailCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ApplyGuardrailCommandInput,
    ApplyGuardrailCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ApplyGuardrailCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ApplyGuardrailCommandInput,
    ApplyGuardrailCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ApplyGuardrailCommand extends ApplyGuardrailCommand_base {
  protected static __types: {
    api: {
      input: ApplyGuardrailRequest;
      output: ApplyGuardrailResponse;
    };
    sdk: {
      input: ApplyGuardrailCommandInput;
      output: ApplyGuardrailCommandOutput;
    };
  };
}
