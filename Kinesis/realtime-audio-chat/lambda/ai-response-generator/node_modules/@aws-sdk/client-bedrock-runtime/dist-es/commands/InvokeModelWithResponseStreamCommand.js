import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { InvokeModelWithResponseStreamRequestFilterSensitiveLog, InvokeModelWithResponseStreamResponseFilterSensitiveLog, } from "../models/models_0";
import { de_InvokeModelWithResponseStreamCommand, se_InvokeModelWithResponseStreamCommand, } from "../protocols/Aws_restJson1";
export { $Command };
export class InvokeModelWithResponseStreamCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonBedrockFrontendService", "InvokeModelWithResponseStream", {
    eventStream: {
        output: true,
    },
})
    .n("BedrockRuntimeClient", "InvokeModelWithResponseStreamCommand")
    .f(InvokeModelWithResponseStreamRequestFilterSensitiveLog, InvokeModelWithResponseStreamResponseFilterSensitiveLog)
    .ser(se_InvokeModelWithResponseStreamCommand)
    .de(de_InvokeModelWithResponseStreamCommand)
    .build() {
}
