import React from 'react';
import './App.css';
import { Config, logger, AudioUtils } from './utils';
import { ConversationUI, ErrorBoundary, ServiceStatus, GracefulDegradation, PerformanceIndicator, AudioControls, ThemeToggle } from './components/ui';
import { Header, Footer, Sidebar } from './components/layout';
import { Message, ErrorInfo } from './types';
import { ThemeProvider } from './contexts/ThemeContext';

function App() {
  // Layout state
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);
  const [activeSection, setActiveSection] = React.useState('chat');

  // Sample messages for demonstrating the ConversationUI
  const [messages, setMessages] = React.useState<Message[]>([
    {
      messageId: '1',
      sessionId: 'demo-session',
      type: 'user',
      content: 'Hello! Can you hear me?',
      timestamp: Date.now() - 10000,
      confidence: 0.95,
      processingLatency: 120
    },
    {
      messageId: '2',
      sessionId: 'demo-session',
      type: 'assistant',
      content: 'Yes, I can hear you perfectly! How can I help you today?',
      timestamp: Date.now() - 8000,
      confidence: 0.98,
      processingLatency: 180
    }
  ]);

  const [isProcessing, setIsProcessing] = React.useState(false);
  const [latencyWarning, setLatencyWarning] = React.useState(false);
  const [serviceErrors, setServiceErrors] = React.useState<ErrorInfo[]>([]);

  // Audio controls state
  const [isRecording, setIsRecording] = React.useState(false);
  const [volume, setVolume] = React.useState(1);
  const [isMuted, setIsMuted] = React.useState(false);

  // Connection state
  const [isConnected, setIsConnected] = React.useState(true);
  const [isConnecting, setIsConnecting] = React.useState(false);

  React.useEffect(() => {
    // Verify configuration and audio support on app start
    try {
      const awsConfig = Config.getAWSConfig();
      const audioConfig = Config.getAudioConfig();
      const audioSupport = AudioUtils.checkAudioSupport();

      logger.info('App initialized with configuration:', {
        aws: { region: awsConfig.region, streamArn: awsConfig.streamArn },
        audio: audioConfig,
        audioSupport
      });

      if (!audioSupport.supported) {
        logger.error('Audio not supported:', audioSupport.issues);
      }
    } catch (error) {
      logger.error('Configuration error:', error);
    }

    // Demo: Add a new message after 3 seconds
    const timer = setTimeout(() => {
      setIsProcessing(true);
      setTimeout(() => {
        setMessages(prev => [...prev, {
          messageId: '3',
          sessionId: 'demo-session',
          type: 'user',
          content: 'This is a demo of the conversation UI with enterprise-grade design!',
          timestamp: Date.now(),
          confidence: 0.92,
          processingLatency: 150
        }]);
        setIsProcessing(false);
      }, 2000);
    }, 3000);

    // Demo: Show latency warning after 8 seconds
    const latencyTimer = setTimeout(() => {
      setLatencyWarning(true);
      setTimeout(() => setLatencyWarning(false), 5000);
    }, 8000);

    // Demo: Simulate connection state changes
    const connectionTimer = setTimeout(() => {
      setIsConnecting(true);
      setIsConnected(false);
      setTimeout(() => {
        setIsConnecting(false);
        setIsConnected(true);
      }, 3000);
    }, 12000);

    return () => {
      clearTimeout(timer);
      clearTimeout(latencyTimer);
      clearTimeout(connectionTimer);
    };
  }, []);

  const handleServiceRetry = async (serviceName: string) => {
    logger.info(`Retrying service: ${serviceName}`);
    // Remove errors for the specific service
    setServiceErrors(prev => prev.filter(error =>
      !error.code.toLowerCase().includes(serviceName.toLowerCase())
    ));
  };

  const handleSettingsClick = () => {
    setActiveSection('settings');
  };

  const handleHelpClick = () => {
    console.log('Help clicked');
  };

  // Audio control handlers
  const handleStartRecording = () => {
    setIsRecording(true);
    logger.info('Recording started');
  };

  const handleStopRecording = () => {
    setIsRecording(false);
    logger.info('Recording stopped');
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    if (newVolume > 0 && isMuted) {
      setIsMuted(false);
    }
  };

  const handleMuteToggle = () => {
    setIsMuted(!isMuted);
  };

  const handleClearConversation = () => {
    setMessages([]);
    logger.info('Conversation cleared');
  };

  const renderMainContent = () => {
    switch (activeSection) {
      case 'chat':
        return (
          <div className="chat-section">
            <div className="section-header">
              <h2>Real-time Audio Chat</h2>
              <p>Experience AI-powered conversations with enterprise-grade reliability</p>
            </div>

            <div className="chat-container">
              <AudioControls
                isRecording={isRecording}
                volume={volume}
                isMuted={isMuted}
                onStartRecording={handleStartRecording}
                onStopRecording={handleStopRecording}
                onVolumeChange={handleVolumeChange}
                onMuteToggle={handleMuteToggle}
                onClearConversation={handleClearConversation}
                disabled={serviceErrors.length > 0}
              />

              <GracefulDegradation
                serviceErrors={serviceErrors}
                onServiceRetry={handleServiceRetry}
                showDegradationNotice={true}
              >
                <ConversationUI
                  messages={messages}
                  isProcessing={isProcessing}
                  latencyWarning={latencyWarning}
                />
              </GracefulDegradation>
            </div>
          </div>
        );

      case 'analytics':
        return (
          <div className="analytics-section">
            <div className="section-header">
              <h2>Performance Analytics</h2>
              <p>Monitor system performance and conversation metrics</p>
            </div>

            <div className="analytics-grid">
              <div className="analytics-card">
                <PerformanceIndicator />
              </div>
              <div className="analytics-card">
                <ServiceStatus
                  autoRefresh={true}
                  refreshInterval={30000}
                  showDetails={true}
                  compact={false}
                />
              </div>
            </div>
          </div>
        );

      case 'history':
        return (
          <div className="history-section">
            <div className="section-header">
              <h2>Conversation History</h2>
              <p>Review past conversations and interactions</p>
            </div>

            <div className="history-placeholder">
              <div className="placeholder-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
                  <polyline points="12,6 12,12 16,14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <h3>History Coming Soon</h3>
              <p>Conversation history and analytics will be available in the next release.</p>
            </div>
          </div>
        );

      case 'settings':
        return (
          <div className="settings-section">
            <div className="section-header">
              <h2>System Settings</h2>
              <p>Configure your audio chat experience</p>
            </div>

            <div className="settings-grid">
              <div className="settings-card">
                <h3>Audio Configuration</h3>
                <div className="setting-item">
                  <label>Sample Rate</label>
                  <select>
                    <option value="16000">16 kHz (Recommended)</option>
                    <option value="44100">44.1 kHz</option>
                    <option value="48000">48 kHz</option>
                  </select>
                </div>
                <div className="setting-item">
                  <label>Noise Suppression</label>
                  <input type="checkbox" defaultChecked />
                </div>
              </div>

              <div className="settings-card">
                <h3>AI Configuration</h3>
                <div className="setting-item">
                  <label>Response Speed</label>
                  <select>
                    <option value="fast">Fast</option>
                    <option value="balanced" selected>Balanced</option>
                    <option value="accurate">Accurate</option>
                  </select>
                </div>
                <div className="setting-item">
                  <label>Language Model</label>
                  <select>
                    <option value="gpt-4">GPT-4 (Recommended)</option>
                    <option value="gpt-3.5">GPT-3.5 Turbo</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <ThemeProvider>
      <ErrorBoundary>
        <div className="enterprise-app">
          <Header
            onSettingsClick={handleSettingsClick}
            onHelpClick={handleHelpClick}
            isConnected={isConnected}
            isConnecting={isConnecting}
          />

          <div className="app-layout">
            <Sidebar
              isCollapsed={sidebarCollapsed}
              onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
              activeSection={activeSection}
              onSectionChange={setActiveSection}
            />

            <main className="main-content">
              <div className="content-container">
                {renderMainContent()}
              </div>
            </main>
          </div>

          <Footer />

          {/* Floating theme toggle */}
          <div className="floating-theme-toggle">
            <ThemeToggle size="medium" />
          </div>
        </div>
      </ErrorBoundary>
    </ThemeProvider>
  );
}

export default App;
