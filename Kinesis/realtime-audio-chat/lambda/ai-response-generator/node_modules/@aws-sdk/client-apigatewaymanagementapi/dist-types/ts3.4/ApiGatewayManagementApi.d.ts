import { HttpHandlerOptions as __HttpHandlerOptions } from "@smithy/types";
import { ApiGatewayManagementApiClient } from "./ApiGatewayManagementApiClient";
import {
  DeleteConnectionCommandInput,
  DeleteConnectionCommandOutput,
} from "./commands/DeleteConnectionCommand";
import {
  GetConnectionCommandInput,
  GetConnectionCommandOutput,
} from "./commands/GetConnectionCommand";
import {
  PostToConnectionCommandInput,
  PostToConnectionCommandOutput,
} from "./commands/PostToConnectionCommand";
export interface ApiGatewayManagementApi {
  deleteConnection(
    args: DeleteConnectionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<DeleteConnectionCommandOutput>;
  deleteConnection(
    args: DeleteConnectionCommandInput,
    cb: (err: any, data?: DeleteConnectionCommandOutput) => void
  ): void;
  deleteConnection(
    args: DeleteConnectionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: DeleteConnectionCommandOutput) => void
  ): void;
  getConnection(
    args: GetConnectionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<GetConnectionCommandOutput>;
  getConnection(
    args: GetConnectionCommandInput,
    cb: (err: any, data?: GetConnectionCommandOutput) => void
  ): void;
  getConnection(
    args: GetConnectionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: GetConnectionCommandOutput) => void
  ): void;
  postToConnection(
    args: PostToConnectionCommandInput,
    options?: __HttpHandlerOptions
  ): Promise<PostToConnectionCommandOutput>;
  postToConnection(
    args: PostToConnectionCommandInput,
    cb: (err: any, data?: PostToConnectionCommandOutput) => void
  ): void;
  postToConnection(
    args: PostToConnectionCommandInput,
    options: __HttpHandlerOptions,
    cb: (err: any, data?: PostToConnectionCommandOutput) => void
  ): void;
}
export declare class ApiGatewayManagementApi
  extends ApiGatewayManagementApiClient
  implements ApiGatewayManagementApi {}
