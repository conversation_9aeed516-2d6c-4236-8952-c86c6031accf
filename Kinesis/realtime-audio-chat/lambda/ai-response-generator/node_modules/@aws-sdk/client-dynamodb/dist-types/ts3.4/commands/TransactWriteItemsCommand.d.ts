import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import { TransactWriteItemsOutput } from "../models/models_0";
import { TransactWriteItemsInput } from "../models/models_1";
export { __MetadataBearer };
export { $Command };
export interface TransactWriteItemsCommandInput
  extends TransactWriteItemsInput {}
export interface TransactWriteItemsCommandOutput
  extends TransactWriteItemsOutput,
    __MetadataBearer {}
declare const TransactWriteItemsCommand_base: {
  new (
    input: TransactWriteItemsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    TransactWriteItemsCommandInput,
    TransactWriteItemsCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: TransactWriteItemsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    TransactWriteItemsCommandInput,
    TransactWriteItemsCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class TransactWriteItemsCommand extends TransactWriteItemsCommand_base {
  protected static __types: {
    api: {
      input: TransactWriteItemsInput;
      output: TransactWriteItemsOutput;
    };
    sdk: {
      input: TransactWriteItemsCommandInput;
      output: TransactWriteItemsCommandOutput;
    };
  };
}
