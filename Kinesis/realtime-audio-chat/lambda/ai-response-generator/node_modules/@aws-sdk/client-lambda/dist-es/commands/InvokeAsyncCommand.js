import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { InvokeAsyncRequestFilterSensitiveLog } from "../models/models_0";
import { de_InvokeAsyncCommand, se_InvokeAsyncCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class InvokeAsyncCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "InvokeAsync", {})
    .n("LambdaClient", "InvokeAsyncCommand")
    .f(InvokeAsyncRequestFilterSensitiveLog, void 0)
    .ser(se_InvokeAsyncCommand)
    .de(de_InvokeAsyncCommand)
    .build() {
}
