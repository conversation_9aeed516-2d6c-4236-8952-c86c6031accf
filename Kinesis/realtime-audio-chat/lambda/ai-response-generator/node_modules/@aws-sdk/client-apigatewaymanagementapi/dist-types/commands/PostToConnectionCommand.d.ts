import { Command as $Command } from "@smithy/smithy-client";
import { BlobPayloadInputTypes, MetadataBearer as __MetadataBearer } from "@smithy/types";
import { ApiGatewayManagementApiClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../ApiGatewayManagementApiClient";
import { PostToConnectionRequest } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 */
export type PostToConnectionCommandInputType = Omit<PostToConnectionRequest, "Data"> & {
    Data: BlobPayloadInputTypes;
};
/**
 * @public
 *
 * The input for {@link PostToConnectionCommand}.
 */
export interface PostToConnectionCommandInput extends PostToConnectionCommandInputType {
}
/**
 * @public
 *
 * The output of {@link PostToConnectionCommand}.
 */
export interface PostToConnectionCommandOutput extends __MetadataBearer {
}
declare const PostToConnectionCommand_base: {
    new (input: PostToConnectionCommandInput): import("@smithy/smithy-client").CommandImpl<PostToConnectionCommandInput, PostToConnectionCommandOutput, ApiGatewayManagementApiClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: PostToConnectionCommandInput): import("@smithy/smithy-client").CommandImpl<PostToConnectionCommandInput, PostToConnectionCommandOutput, ApiGatewayManagementApiClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Sends the provided data to the specified connection.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { ApiGatewayManagementApiClient, PostToConnectionCommand } from "@aws-sdk/client-apigatewaymanagementapi"; // ES Modules import
 * // const { ApiGatewayManagementApiClient, PostToConnectionCommand } = require("@aws-sdk/client-apigatewaymanagementapi"); // CommonJS import
 * const client = new ApiGatewayManagementApiClient(config);
 * const input = { // PostToConnectionRequest
 *   Data: new Uint8Array(), // e.g. Buffer.from("") or new TextEncoder().encode("")   // required
 *   ConnectionId: "STRING_VALUE", // required
 * };
 * const command = new PostToConnectionCommand(input);
 * const response = await client.send(command);
 * // {};
 *
 * ```
 *
 * @param PostToConnectionCommandInput - {@link PostToConnectionCommandInput}
 * @returns {@link PostToConnectionCommandOutput}
 * @see {@link PostToConnectionCommandInput} for command's `input` shape.
 * @see {@link PostToConnectionCommandOutput} for command's `response` shape.
 * @see {@link ApiGatewayManagementApiClientResolvedConfig | config} for ApiGatewayManagementApiClient's `config` shape.
 *
 * @throws {@link ForbiddenException} (client fault)
 *  <p>The caller is not authorized to invoke this operation.</p>
 *
 * @throws {@link GoneException} (client fault)
 *  <p>The connection with the provided id no longer exists.</p>
 *
 * @throws {@link LimitExceededException} (client fault)
 *  <p>The client is sending more than the allowed number of requests per unit of time or the WebSocket client side buffer is full.</p>
 *
 * @throws {@link PayloadTooLargeException} (client fault)
 *  <p>The data has exceeded the maximum size allowed.</p>
 *
 * @throws {@link ApiGatewayManagementApiServiceException}
 * <p>Base exception class for all service exceptions from ApiGatewayManagementApi service.</p>
 *
 *
 * @public
 */
export declare class PostToConnectionCommand extends PostToConnectionCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: PostToConnectionRequest;
            output: {};
        };
        sdk: {
            input: PostToConnectionCommandInput;
            output: PostToConnectionCommandOutput;
        };
    };
}
