import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ListVersionsByFunctionResponseFilterSensitiveLog, } from "../models/models_0";
import { de_ListVersionsByFunctionCommand, se_ListVersionsByFunctionCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ListVersionsByFunctionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "ListVersionsByFunction", {})
    .n("LambdaClient", "ListVersionsByFunctionCommand")
    .f(void 0, ListVersionsByFunctionResponseFilterSensitiveLog)
    .ser(se_ListVersionsByFunctionCommand)
    .de(de_ListVersionsByFunctionCommand)
    .build() {
}
