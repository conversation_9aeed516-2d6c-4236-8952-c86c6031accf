/**
 * <p>The Amazon API Gateway Management API allows you to directly manage runtime aspects of your deployed APIs. To use it, you must explicitly set the SDK's endpoint to point to the endpoint of your deployed API. The endpoint will be of the form https://{api-id}.execute-api.{region}.amazonaws.com/{stage}, or will be the endpoint corresponding to your API's custom domain and base path, if applicable.</p>
 *
 * @packageDocumentation
 */
export * from "./ApiGatewayManagementApiClient";
export * from "./ApiGatewayManagementApi";
export { ClientInputEndpointParameters } from "./endpoint/EndpointParameters";
export type { RuntimeExtension } from "./runtimeExtensions";
export type { ApiGatewayManagementApiExtensionConfiguration } from "./extensionConfiguration";
export * from "./commands";
export * from "./models";
export { ApiGatewayManagementApiServiceException } from "./models/ApiGatewayManagementApiServiceException";
