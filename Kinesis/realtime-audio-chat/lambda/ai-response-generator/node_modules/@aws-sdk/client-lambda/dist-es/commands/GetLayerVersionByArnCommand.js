import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_GetLayerVersionByArnCommand, se_GetLayerVersionByArnCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetLayerVersionByArnCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "GetLayerVersionByArn", {})
    .n("LambdaClient", "GetLayerVersionByArnCommand")
    .f(void 0, void 0)
    .ser(se_GetLayerVersionByArnCommand)
    .de(de_GetLayerVersionByArnCommand)
    .build() {
}
