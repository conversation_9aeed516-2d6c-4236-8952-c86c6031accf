import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { FunctionConfigurationFilterSensitiveLog, } from "../models/models_0";
import { de_GetFunctionConfigurationCommand, se_GetFunctionConfigurationCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetFunctionConfigurationCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "GetFunctionConfiguration", {})
    .n("LambdaClient", "GetFunctionConfigurationCommand")
    .f(void 0, FunctionConfigurationFilterSensitiveLog)
    .ser(se_GetFunctionConfigurationCommand)
    .de(de_GetFunctionConfigurationCommand)
    .build() {
}
