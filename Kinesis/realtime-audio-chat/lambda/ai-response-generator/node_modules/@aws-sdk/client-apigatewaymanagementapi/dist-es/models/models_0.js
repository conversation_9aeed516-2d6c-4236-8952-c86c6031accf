import { ApiGatewayManagementApiServiceException as __BaseException } from "./ApiGatewayManagementApiServiceException";
export class ForbiddenException extends __BaseException {
    name = "ForbiddenException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "ForbiddenException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ForbiddenException.prototype);
    }
}
export class GoneException extends __BaseException {
    name = "GoneException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "GoneException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, GoneException.prototype);
    }
}
export class LimitExceededException extends __BaseException {
    name = "LimitExceededException";
    $fault = "client";
    constructor(opts) {
        super({
            name: "LimitExceededException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, LimitExceededException.prototype);
    }
}
export class PayloadTooLargeException extends __BaseException {
    name = "PayloadTooLargeException";
    $fault = "client";
    Message;
    constructor(opts) {
        super({
            name: "PayloadTooLargeException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, PayloadTooLargeException.prototype);
        this.Message = opts.Message;
    }
}
