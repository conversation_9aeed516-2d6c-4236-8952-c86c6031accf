#!/bin/bash

# Script to extract configuration values from deployed CDK stack

set -e

STACK_NAME="RealtimeAudioChatStack"
CONFIG_FILE="../src/config/aws-config.json"

echo "📋 Extracting configuration from deployed stack..."

# Get stack outputs
WEBSOCKET_ENDPOINT=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='WebSocketApiEndpoint'].OutputValue" --output text)
SESSIONS_TABLE=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='ConversationSessionsTableName'].OutputValue" --output text)
MESSAGES_TABLE=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='MessagesTableName'].OutputValue" --output text)
STREAM_PROCESSOR=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='StreamProcessorFunctionName'].OutputValue" --output text)
TRANSCRIPTION_HANDLER=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='TranscriptionHandlerFunctionName'].OutputValue" --output text)
AI_RESPONSE_GENERATOR=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='AIResponseGeneratorFunctionName'].OutputValue" --output text)
AUDIO_SYNTHESIZER=$(aws cloudformation describe-stacks --stack-name $STACK_NAME --query "Stacks[0].Outputs[?OutputKey=='AudioSynthesizerFunctionName'].OutputValue" --output text)

# Create config directory if it doesn't exist
mkdir -p ../src/config

# Generate configuration file
cat > $CONFIG_FILE << EOF
{
  "websocketEndpoint": "$WEBSOCKET_ENDPOINT",
  "region": "us-east-1",
  "dynamodbTables": {
    "sessions": "$SESSIONS_TABLE",
    "messages": "$MESSAGES_TABLE"
  },
  "lambdaFunctions": {
    "streamProcessor": "$STREAM_PROCESSOR",
    "transcriptionHandler": "$TRANSCRIPTION_HANDLER",
    "aiResponseGenerator": "$AI_RESPONSE_GENERATOR",
    "audioSynthesizer": "$AUDIO_SYNTHESIZER"
  },
  "kinesisVideoStream": {
    "arn": "arn:aws:kinesisvideo:us-east-1:122610497322:stream/my-webrtc-channel/1755712113724",
    "name": "my-webrtc-channel"
  },
  "awsServices": {
    "transcribe": {
      "languageCode": "en-US",
      "sampleRate": 16000
    },
    "bedrock": {
      "modelId": "anthropic.claude-3-sonnet-20240229-v1:0",
      "maxTokens": 150,
      "temperature": 0.7
    },
    "polly": {
      "voiceId": "Joanna",
      "outputFormat": "mp3",
      "engine": "neural"
    }
  }
}
EOF

echo "✅ Configuration file created at: $CONFIG_FILE"
echo ""
echo "📋 Extracted values:"
echo "  WebSocket Endpoint: $WEBSOCKET_ENDPOINT"
echo "  Sessions Table: $SESSIONS_TABLE"
echo "  Messages Table: $MESSAGES_TABLE"
echo "  Stream Processor: $STREAM_PROCESSOR"
echo "  Transcription Handler: $TRANSCRIPTION_HANDLER"
echo "  AI Response Generator: $AI_RESPONSE_GENERATOR"
echo "  Audio Synthesizer: $AUDIO_SYNTHESIZER"