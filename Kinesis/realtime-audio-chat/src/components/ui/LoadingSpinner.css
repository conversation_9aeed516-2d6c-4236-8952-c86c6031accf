/* Loading Spinner Styles */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.spinner-circle {
  position: relative;
  display: inline-block;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-right-color: currentColor;
  animation: spin 0.8s linear infinite reverse;
}

/* Size variants */
.spinner-small .spinner-circle {
  width: 16px;
  height: 16px;
}

.spinner-small .spinner-inner {
  border-width: 1.5px;
}

.spinner-medium .spinner-circle {
  width: 24px;
  height: 24px;
}

.spinner-medium .spinner-inner {
  border-width: 2px;
}

.spinner-large .spinner-circle {
  width: 32px;
  height: 32px;
}

.spinner-large .spinner-inner {
  border-width: 3px;
}

/* Color variants */
.spinner-primary {
  color: #667eea;
}

.spinner-secondary {
  color: #64748b;
}

.spinner-white {
  color: #ffffff;
}

/* Spinner text */
.spinner-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: currentColor;
  text-align: center;
  opacity: 0.8;
}

/* Overlay variant */
.spinner-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.spinner-overlay .loading-spinner {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animation keyframes */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Pulse animation for text */
.spinner-text {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .spinner-overlay {
    background: rgba(0, 0, 0, 0.9);
  }
  
  .spinner-overlay .loading-spinner {
    background: rgba(30, 41, 59, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .spinner-circle,
  .spinner-inner {
    animation-duration: 2s;
  }
  
  .spinner-text {
    animation: none;
    opacity: 0.8;
  }
}
