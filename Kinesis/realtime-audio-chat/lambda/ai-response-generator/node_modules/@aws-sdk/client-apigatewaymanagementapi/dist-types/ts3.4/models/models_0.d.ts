import { ExceptionOptionType as __ExceptionOptionType } from "@smithy/smithy-client";
import { ApiGatewayManagementApiServiceException as __BaseException } from "./ApiGatewayManagementApiServiceException";
export interface DeleteConnectionRequest {
  ConnectionId: string | undefined;
}
export declare class ForbiddenException extends __BaseException {
  readonly name: "ForbiddenException";
  readonly $fault: "client";
  constructor(opts: __ExceptionOptionType<ForbiddenException, __BaseException>);
}
export declare class GoneException extends __BaseException {
  readonly name: "GoneException";
  readonly $fault: "client";
  constructor(opts: __ExceptionOptionType<GoneException, __BaseException>);
}
export declare class LimitExceededException extends __BaseException {
  readonly name: "LimitExceededException";
  readonly $fault: "client";
  constructor(
    opts: __ExceptionOptionType<LimitExceededException, __BaseException>
  );
}
export interface GetConnectionRequest {
  ConnectionId: string | undefined;
}
export interface Identity {
  SourceIp: string | undefined;
  UserAgent: string | undefined;
}
export interface GetConnectionResponse {
  ConnectedAt?: Date | undefined;
  Identity?: Identity | undefined;
  LastActiveAt?: Date | undefined;
}
export declare class PayloadTooLargeException extends __BaseException {
  readonly name: "PayloadTooLargeException";
  readonly $fault: "client";
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<PayloadTooLargeException, __BaseException>
  );
}
export interface PostToConnectionRequest {
  Data: Uint8Array | undefined;
  ConnectionId: string | undefined;
}
