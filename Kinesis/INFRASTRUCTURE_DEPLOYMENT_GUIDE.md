# 🚀 Complete Infrastructure Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the realtime audio chat application infrastructure on AWS.

## 📋 Prerequisites

### 1. AWS Account Setup
- Active AWS account with billing enabled
- AWS CLI installed and configured
- Appropriate IAM permissions for creating resources

### 2. Development Environment
```bash
# Check Node.js version (18+ required)
node --version

# Install AWS CDK globally
npm install -g aws-cdk

# Verify CDK installation
cdk --version
```

### 3. AWS Permissions Required
Your AWS user/role needs permissions for:
- CloudFormation (full access)
- Lambda (full access)
- DynamoDB (full access)
- API Gateway (full access)
- IAM (create/manage roles)
- Kinesis Video Streams (read access)
- AWS Transcribe (invoke permissions)
- AWS Bedrock (invoke permissions)
- Amazon Polly (invoke permissions)

## 🏗️ Infrastructure Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   React App     │───▶│ Kinesis Video    │───▶│ Stream          │
│   (Frontend)    │    │ Streams          │    │ Processor       │
└─────────────────┘    └──────────────────┘    │ Lambda          │
                                               └─────────────────┘
                                                        │
                       ┌─────────────────┐             ▼
                       │ WebSocket API   │    ┌─────────────────┐
                       │ Gateway         │◀───│ Transcription   │
                       └─────────────────┘    │ Handler Lambda  │
                                               └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐             ▼
│ DynamoDB        │◀───│ AI Response     │    ┌─────────────────┐
│ Tables          │    │ Generator       │◀───│ AWS Transcribe  │
└─────────────────┘    │ Lambda          │    │ Streaming       │
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Audio           │───▶│ Amazon Polly    │
                       │ Synthesizer     │    │ TTS             │
                       │ Lambda          │    └─────────────────┘
                       └─────────────────┘
```

## 🚀 Deployment Steps

### Step 1: Navigate to Infrastructure Directory
```bash
cd Kinesis/realtime-audio-chat/infrastructure
```

### Step 2: Install Dependencies
```bash
npm install
```

### Step 3: Bootstrap CDK (First Time Only)
```bash
# Bootstrap CDK for your AWS account/region
npx cdk bootstrap

# Verify bootstrap
npx cdk list
```

### Step 4: Deploy Infrastructure
```bash
# Make deployment script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

The deployment script will:
1. Build the CDK project
2. Synthesize CloudFormation templates
3. Deploy the infrastructure stack
4. Display stack outputs

### Step 5: Extract Configuration
```bash
# Make extraction script executable
chmod +x extract-config.sh

# Extract configuration for frontend
./extract-config.sh
```

This creates `../src/config/aws-config.json` with:
- WebSocket API endpoint
- DynamoDB table names
- Lambda function names
- AWS region configuration

### Step 6: Test Infrastructure
```bash
# Make test script executable
chmod +x test-infrastructure.sh

# Run infrastructure tests
./test-infrastructure.sh
```

## 📊 What Gets Created

### AWS Resources
1. **DynamoDB Tables** (2)
   - `realtime-audio-chat-sessions`
   - `realtime-audio-chat-messages`

2. **Lambda Functions** (4)
   - `realtime-audio-chat-stream-processor`
   - `realtime-audio-chat-transcription-handler`
   - `realtime-audio-chat-ai-response-generator`
   - `realtime-audio-chat-audio-synthesizer`

3. **API Gateway WebSocket API** (1)
   - Real-time communication endpoint

4. **IAM Roles and Policies**
   - Lambda execution role with required permissions

5. **CloudWatch Log Groups** (4)
   - One for each Lambda function

### Estimated Costs
- **DynamoDB**: Pay-per-request (minimal for testing)
- **Lambda**: Pay-per-invocation (free tier covers development)
- **API Gateway**: Pay-per-connection/message
- **AWS Services**: Transcribe, Bedrock, Polly usage-based

## 🔧 Configuration Details

### Environment Variables Set
```bash
# Stream Processor
SESSIONS_TABLE=realtime-audio-chat-sessions
MESSAGES_TABLE=realtime-audio-chat-messages
STREAM_ARN=arn:aws:kinesisvideo:us-east-1:122610497322:stream/my-webrtc-channel/1755712113724

# Transcription Handler
AI_RESPONSE_GENERATOR_FUNCTION=realtime-audio-chat-ai-response-generator
WEBSOCKET_API_ENDPOINT=[auto-generated]

# AI Response Generator
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
AUDIO_SYNTHESIZER_FUNCTION=realtime-audio-chat-audio-synthesizer

# Audio Synthesizer
WEBSOCKET_API_ENDPOINT=[auto-generated]
```

### Service Configurations
- **Transcribe**: 16kHz, en-US, streaming enabled
- **Bedrock**: Claude-3-Sonnet model
- **Polly**: Neural voice (Joanna)
- **DynamoDB**: Pay-per-request billing

## 🔍 Verification Steps

### 1. Check AWS Console
- Navigate to CloudFormation → Stacks
- Verify "RealtimeAudioChatStack" status is "CREATE_COMPLETE"

### 2. Test Lambda Functions
```bash
# Test stream processor
aws lambda invoke --function-name realtime-audio-chat-stream-processor --payload '{}' response.json

# Check response
cat response.json
```

### 3. Verify DynamoDB Tables
```bash
# List tables
aws dynamodb list-tables

# Describe sessions table
aws dynamodb describe-table --table-name realtime-audio-chat-sessions
```

### 4. Test WebSocket API
```bash
# Get WebSocket endpoint from stack outputs
aws cloudformation describe-stacks --stack-name RealtimeAudioChatStack --query 'Stacks[0].Outputs'
```

## 🚨 Troubleshooting

### Common Issues

1. **CDK Bootstrap Required**
   ```bash
   Error: Need to perform AWS CDK bootstrap
   Solution: npx cdk bootstrap
   ```

2. **Insufficient Permissions**
   ```bash
   Error: User is not authorized to perform action
   Solution: Check IAM permissions, ensure CloudFormation access
   ```

3. **Region Mismatch**
   ```bash
   Error: Stack in different region
   Solution: Set AWS_DEFAULT_REGION=us-east-1
   ```

4. **Lambda Build Failures**
   ```bash
   Error: npm ci failed in Lambda function
   Solution: Check Lambda function package.json files
   ```

### Debug Commands
```bash
# Check CDK diff
npx cdk diff

# Synthesize without deploying
npx cdk synth

# View CloudFormation events
aws cloudformation describe-stack-events --stack-name RealtimeAudioChatStack
```

## 🧹 Cleanup

To remove all infrastructure:
```bash
# Destroy the stack
npx cdk destroy

# Confirm deletion
# Type 'y' when prompted
```

## 📚 Next Steps

After successful deployment:

1. **Update Frontend Configuration**
   - Copy generated `aws-config.json` to frontend
   - Update environment variables

2. **Implement Lambda Functions**
   - Add actual processing logic
   - Test with real audio data

3. **Configure Monitoring**
   - Set up CloudWatch alarms
   - Configure error notifications

4. **Security Hardening**
   - Review IAM permissions
   - Enable VPC if required
   - Configure API authentication

## 🔗 Useful Links

- [AWS CDK Documentation](https://docs.aws.amazon.com/cdk/)
- [AWS Lambda Best Practices](https://docs.aws.amazon.com/lambda/latest/dg/best-practices.html)
- [DynamoDB Best Practices](https://docs.aws.amazon.com/amazondynamodb/latest/developerguide/best-practices.html)
- [API Gateway WebSocket APIs](https://docs.aws.amazon.com/apigateway/latest/developerguide/websocket-api.html)
