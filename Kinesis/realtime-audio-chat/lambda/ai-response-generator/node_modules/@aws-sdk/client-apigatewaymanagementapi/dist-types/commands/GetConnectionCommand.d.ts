import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { ApiGatewayManagementApiClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../ApiGatewayManagementApiClient";
import { GetConnectionRequest, GetConnectionResponse } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link GetConnectionCommand}.
 */
export interface GetConnectionCommandInput extends GetConnectionRequest {
}
/**
 * @public
 *
 * The output of {@link GetConnectionCommand}.
 */
export interface GetConnectionCommandOutput extends GetConnectionResponse, __MetadataBearer {
}
declare const GetConnectionCommand_base: {
    new (input: GetConnectionCommandInput): import("@smithy/smithy-client").CommandImpl<GetConnectionCommandInput, GetConnectionCommandOutput, ApiGatewayManagementApiClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: GetConnectionCommandInput): import("@smithy/smithy-client").CommandImpl<GetConnectionCommandInput, GetConnectionCommandOutput, ApiGatewayManagementApiClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Get information about the connection with the provided id.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { ApiGatewayManagementApiClient, GetConnectionCommand } from "@aws-sdk/client-apigatewaymanagementapi"; // ES Modules import
 * // const { ApiGatewayManagementApiClient, GetConnectionCommand } = require("@aws-sdk/client-apigatewaymanagementapi"); // CommonJS import
 * const client = new ApiGatewayManagementApiClient(config);
 * const input = { // GetConnectionRequest
 *   ConnectionId: "STRING_VALUE", // required
 * };
 * const command = new GetConnectionCommand(input);
 * const response = await client.send(command);
 * // { // GetConnectionResponse
 * //   ConnectedAt: new Date("TIMESTAMP"),
 * //   Identity: { // Identity
 * //     SourceIp: "STRING_VALUE", // required
 * //     UserAgent: "STRING_VALUE", // required
 * //   },
 * //   LastActiveAt: new Date("TIMESTAMP"),
 * // };
 *
 * ```
 *
 * @param GetConnectionCommandInput - {@link GetConnectionCommandInput}
 * @returns {@link GetConnectionCommandOutput}
 * @see {@link GetConnectionCommandInput} for command's `input` shape.
 * @see {@link GetConnectionCommandOutput} for command's `response` shape.
 * @see {@link ApiGatewayManagementApiClientResolvedConfig | config} for ApiGatewayManagementApiClient's `config` shape.
 *
 * @throws {@link ForbiddenException} (client fault)
 *  <p>The caller is not authorized to invoke this operation.</p>
 *
 * @throws {@link GoneException} (client fault)
 *  <p>The connection with the provided id no longer exists.</p>
 *
 * @throws {@link LimitExceededException} (client fault)
 *  <p>The client is sending more than the allowed number of requests per unit of time or the WebSocket client side buffer is full.</p>
 *
 * @throws {@link ApiGatewayManagementApiServiceException}
 * <p>Base exception class for all service exceptions from ApiGatewayManagementApi service.</p>
 *
 *
 * @public
 */
export declare class GetConnectionCommand extends GetConnectionCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: GetConnectionRequest;
            output: GetConnectionResponse;
        };
        sdk: {
            input: GetConnectionCommandInput;
            output: GetConnectionCommandOutput;
        };
    };
}
