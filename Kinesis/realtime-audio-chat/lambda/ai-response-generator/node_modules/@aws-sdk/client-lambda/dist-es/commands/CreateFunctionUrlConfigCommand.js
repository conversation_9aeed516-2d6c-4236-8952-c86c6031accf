import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_CreateFunctionUrlConfigCommand, se_CreateFunctionUrlConfigCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class CreateFunctionUrlConfigCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "CreateFunctionUrlConfig", {})
    .n("LambdaClient", "CreateFunctionUrlConfigCommand")
    .f(void 0, void 0)
    .ser(se_CreateFunctionUrlConfigCommand)
    .de(de_CreateFunctionUrlConfigCommand)
    .build() {
}
