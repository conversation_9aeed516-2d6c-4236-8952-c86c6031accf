import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockRuntimeClient";
import {
  StartAsyncInvokeRequest,
  StartAsyncInvokeResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface StartAsyncInvokeCommandInput extends StartAsyncInvokeRequest {}
export interface StartAsyncInvokeCommandOutput
  extends StartAsyncInvokeResponse,
    __MetadataBearer {}
declare const StartAsyncInvokeCommand_base: {
  new (
    input: StartAsyncInvokeCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartAsyncInvokeCommandInput,
    StartAsyncInvokeCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartAsyncInvokeCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartAsyncInvokeCommandInput,
    StartAsyncInvokeCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartAsyncInvokeCommand extends StartAsyncInvokeCommand_base {
  protected static __types: {
    api: {
      input: StartAsyncInvokeRequest;
      output: StartAsyncInvokeResponse;
    };
    sdk: {
      input: StartAsyncInvokeCommandInput;
      output: StartAsyncInvokeCommandOutput;
    };
  };
}
