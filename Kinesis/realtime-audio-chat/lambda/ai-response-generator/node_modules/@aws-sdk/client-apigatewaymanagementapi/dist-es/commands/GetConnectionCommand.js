import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_GetConnectionCommand, se_GetConnectionCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetConnectionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("ApiGatewayManagementApi", "GetConnection", {})
    .n("ApiGatewayManagementApiClient", "GetConnectionCommand")
    .f(void 0, void 0)
    .ser(se_GetConnectionCommand)
    .de(de_GetConnectionCommand)
    .build() {
}
