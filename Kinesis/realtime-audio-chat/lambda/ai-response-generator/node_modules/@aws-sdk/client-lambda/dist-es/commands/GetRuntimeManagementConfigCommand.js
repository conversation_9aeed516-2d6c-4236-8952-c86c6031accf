import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_GetRuntimeManagementConfigCommand, se_GetRuntimeManagementConfigCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetRuntimeManagementConfigCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "GetRuntimeManagementConfig", {})
    .n("LambdaClient", "GetRuntimeManagementConfigCommand")
    .f(void 0, void 0)
    .ser(se_GetRuntimeManagementConfigCommand)
    .de(de_GetRuntimeManagementConfigCommand)
    .build() {
}
