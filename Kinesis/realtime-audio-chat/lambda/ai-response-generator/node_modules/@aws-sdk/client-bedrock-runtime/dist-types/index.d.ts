/**
 * <p>Describes the API operations for running inference using Amazon Bedrock models.</p>
 *
 * @packageDocumentation
 */
export * from "./BedrockRuntimeClient";
export * from "./BedrockRuntime";
export { ClientInputEndpointParameters } from "./endpoint/EndpointParameters";
export type { RuntimeExtension } from "./runtimeExtensions";
export type { BedrockRuntimeExtensionConfiguration } from "./extensionConfiguration";
export * from "./commands";
export * from "./pagination";
export * from "./models";
export { BedrockRuntimeServiceException } from "./models/BedrockRuntimeServiceException";
