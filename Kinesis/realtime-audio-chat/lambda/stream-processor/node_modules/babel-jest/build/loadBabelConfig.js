'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
Object.defineProperty(exports, 'loadPartialConfig', {
  enumerable: true,
  get: function () {
    return _core().loadPartialConfig;
  }
});
Object.defineProperty(exports, 'loadPartialConfigAsync', {
  enumerable: true,
  get: function () {
    return _core().loadPartialConfigAsync;
  }
});
function _core() {
  const data = require('@babel/core');
  _core = function () {
    return data;
  };
  return data;
}
