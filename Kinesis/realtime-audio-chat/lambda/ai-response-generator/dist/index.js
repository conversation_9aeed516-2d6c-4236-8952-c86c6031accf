"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handler = void 0;
const client_bedrock_runtime_1 = require("@aws-sdk/client-bedrock-runtime");
const client_dynamodb_1 = require("@aws-sdk/client-dynamodb");
const lib_dynamodb_1 = require("@aws-sdk/lib-dynamodb");
const client_apigatewaymanagementapi_1 = require("@aws-sdk/client-apigatewaymanagementapi");
const client_lambda_1 = require("@aws-sdk/client-lambda");
// Initialize AWS clients
let bedrockClient;
let dynamoClient;
let docClient;
let lambdaClient;
function initializeClients() {
    if (!bedrockClient) {
        bedrockClient = new client_bedrock_runtime_1.BedrockRuntimeClient({ region: process.env.AWS_REGION || 'us-east-1' });
    }
    if (!dynamoClient) {
        dynamoClient = new client_dynamodb_1.DynamoDBClient({ region: process.env.AWS_REGION || 'us-east-1' });
        docClient = lib_dynamodb_1.DynamoDBDocumentClient.from(dynamoClient);
    }
    if (!lambdaClient) {
        lambdaClient = new client_lambda_1.LambdaClient({ region: process.env.AWS_REGION || 'us-east-1' });
    }
}
/**
 * Retrieves conversation context from DynamoDB
 */
async function getConversationContext(sessionId) {
    try {
        const command = new lib_dynamodb_1.GetCommand({
            TableName: process.env.CONVERSATIONS_TABLE,
            Key: { sessionId }
        });
        const result = await docClient.send(command);
        const session = result.Item;
        if (!session) {
            return [];
        }
        // Return last 10 messages for context (to manage token limits)
        return session.messages.slice(-10);
    }
    catch (error) {
        console.error('Error retrieving conversation context:', error);
        return [];
    }
}
/**
 * Builds the prompt for the AI model with conversation context
 */
function buildPrompt(userMessage, conversationHistory) {
    let prompt = "You are a helpful AI assistant engaged in a real-time voice conversation. ";
    prompt += "Respond naturally and conversationally. Keep responses concise but engaging. ";
    prompt += "The user is speaking to you, so respond as if you're having a spoken conversation.\n\n";
    if (conversationHistory.length > 0) {
        prompt += "Conversation history:\n";
        conversationHistory.forEach(msg => {
            const role = msg.type === 'user' ? 'Human' : 'Assistant';
            prompt += `${role}: ${msg.content}\n`;
        });
        prompt += "\n";
    }
    prompt += `Human: ${userMessage}\nAssistant:`;
    return prompt;
}
/**
 * Generates AI response using AWS Bedrock with streaming
 */
async function generateAIResponse(prompt) {
    const modelId = 'anthropic.claude-3-sonnet-20240229-v1:0'; // Using Claude as GPT-OSS-120B may not be available
    const requestBody = {
        anthropic_version: "bedrock-2023-05-31",
        max_tokens: 150,
        temperature: 0.7,
        messages: [
            {
                role: "user",
                content: prompt
            }
        ]
    };
    const input = {
        modelId,
        body: JSON.stringify(requestBody),
        contentType: 'application/json',
        accept: 'application/json'
    };
    try {
        const command = new client_bedrock_runtime_1.InvokeModelWithResponseStreamCommand(input);
        const response = await bedrockClient.send(command);
        let fullResponse = '';
        if (response.body) {
            for await (const chunk of response.body) {
                if (chunk.chunk?.bytes) {
                    const chunkData = JSON.parse(new TextDecoder().decode(chunk.chunk.bytes));
                    if (chunkData.type === 'content_block_delta' && chunkData.delta?.text) {
                        fullResponse += chunkData.delta.text;
                    }
                }
            }
        }
        return fullResponse.trim() || "I'm sorry, I couldn't generate a response at the moment.";
    }
    catch (error) {
        console.error('Error generating AI response:', error);
        return "I'm experiencing some technical difficulties. Please try again.";
    }
}
/**
 * Saves the AI response to DynamoDB
 */
async function saveAIResponse(sessionId, messageId, response) {
    const aiMessage = {
        messageId: `ai_${Date.now()}`,
        sessionId,
        type: 'assistant',
        content: response,
        timestamp: Date.now()
    };
    try {
        // Update the conversation session with the new AI message
        const updateCommand = new lib_dynamodb_1.UpdateCommand({
            TableName: process.env.CONVERSATIONS_TABLE,
            Key: { sessionId },
            UpdateExpression: 'SET messages = list_append(if_not_exists(messages, :empty_list), :new_message), lastActivity = :timestamp',
            ExpressionAttributeValues: {
                ':new_message': [aiMessage],
                ':empty_list': [],
                ':timestamp': Date.now()
            }
        });
        await docClient.send(updateCommand);
    }
    catch (error) {
        console.error('Error saving AI response:', error);
        throw error;
    }
}
/**
 * Sends real-time update via WebSocket
 */
async function sendWebSocketUpdate(connectionId, data) {
    if (!connectionId || !process.env.WEBSOCKET_API_ENDPOINT) {
        return;
    }
    const apiGatewayClient = new client_apigatewaymanagementapi_1.ApiGatewayManagementApiClient({
        endpoint: process.env.WEBSOCKET_API_ENDPOINT
    });
    try {
        const command = new client_apigatewaymanagementapi_1.PostToConnectionCommand({
            ConnectionId: connectionId,
            Data: JSON.stringify(data)
        });
        await apiGatewayClient.send(command);
    }
    catch (error) {
        console.error('Error sending WebSocket update:', error);
        // Don't throw error as WebSocket failures shouldn't break the main flow
    }
}
/**
 * Triggers audio synthesis for the AI response
 */
async function triggerAudioSynthesis(sessionId, messageId, text, connectionId) {
    if (!process.env.AUDIO_SYNTHESIZER_FUNCTION) {
        console.warn('Audio synthesizer function not configured, skipping audio synthesis');
        return;
    }
    try {
        const payload = {
            sessionId,
            messageId,
            text,
            connectionId,
            voiceId: 'Joanna',
            outputFormat: 'mp3' // Default format
        };
        const command = new client_lambda_1.InvokeCommand({
            FunctionName: process.env.AUDIO_SYNTHESIZER_FUNCTION,
            InvocationType: 'Event',
            Payload: JSON.stringify(payload)
        });
        await lambdaClient.send(command);
        console.log(`Triggered audio synthesis for message ${messageId}`);
    }
    catch (error) {
        console.error('Error triggering audio synthesis:', error);
        // Don't throw error as audio synthesis failures shouldn't break the main flow
    }
}
/**
 * Main Lambda handler
 */
const handler = async (event, context) => {
    console.log('AI Response Generator invoked:', JSON.stringify(event, null, 2));
    // Initialize clients
    initializeClients();
    try {
        // Parse the event body
        const requestBody = typeof event.body === 'string'
            ? JSON.parse(event.body)
            : event.body;
        const { sessionId, userMessage, connectionId, messageId } = requestBody;
        if (!sessionId || !userMessage) {
            return {
                statusCode: 400,
                body: JSON.stringify({ error: 'Missing required fields: sessionId, userMessage' })
            };
        }
        const startTime = Date.now();
        // Get conversation context
        const conversationHistory = await getConversationContext(sessionId);
        // Build prompt with context
        const prompt = buildPrompt(userMessage, conversationHistory);
        // Generate AI response
        const aiResponse = await generateAIResponse(prompt);
        // Save response to DynamoDB
        const aiMessageId = `ai_${Date.now()}`;
        await saveAIResponse(sessionId, messageId, aiResponse);
        // Send real-time update via WebSocket
        if (connectionId) {
            await sendWebSocketUpdate(connectionId, {
                type: 'ai_response',
                sessionId,
                messageId: aiMessageId,
                content: aiResponse,
                timestamp: Date.now(),
                processingLatency: Date.now() - startTime
            });
        }
        // Trigger audio synthesis for the AI response
        await triggerAudioSynthesis(sessionId, aiMessageId, aiResponse, connectionId);
        const processingTime = Date.now() - startTime;
        console.log(`AI response generated in ${processingTime}ms`);
        return {
            statusCode: 200,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
                success: true,
                response: aiResponse,
                processingTime,
                sessionId,
                messageId: `ai_${Date.now()}`
            })
        };
    }
    catch (error) {
        console.error('Error in AI response generation:', error);
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
                error: 'Internal server error',
                message: error instanceof Error ? error.message : 'Unknown error'
            })
        };
    }
};
exports.handler = handler;
//# sourceMappingURL=index.js.map