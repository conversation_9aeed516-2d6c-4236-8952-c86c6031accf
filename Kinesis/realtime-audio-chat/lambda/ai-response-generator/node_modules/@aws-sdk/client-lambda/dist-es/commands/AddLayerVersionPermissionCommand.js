import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_AddLayerVersionPermissionCommand, se_AddLayerVersionPermissionCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class AddLayerVersionPermissionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "AddLayerVersionPermission", {})
    .n("LambdaClient", "AddLayerVersionPermissionCommand")
    .f(void 0, void 0)
    .ser(se_AddLayerVersionPermissionCommand)
    .de(de_AddLayerVersionPermissionCommand)
    .build() {
}
