import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  BedrockRuntimeClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../BedrockRuntimeClient";
import {
  InvokeModelWithBidirectionalStreamRequest,
  InvokeModelWithBidirectionalStreamResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface InvokeModelWithBidirectionalStreamCommandInput
  extends InvokeModelWithBidirectionalStreamRequest {}
export interface InvokeModelWithBidirectionalStreamCommandOutput
  extends InvokeModelWithBidirectionalStreamResponse,
    __MetadataBearer {}
declare const InvokeModelWithBidirectionalStreamCommand_base: {
  new (
    input: InvokeModelWithBidirectionalStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    InvokeModelWithBidirectionalStreamCommandInput,
    InvokeModelWithBidirectionalStreamCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: InvokeModelWithBidirectionalStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    InvokeModelWithBidirectionalStreamCommandInput,
    InvokeModelWithBidirectionalStreamCommandOutput,
    BedrockRuntimeClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class InvokeModelWithBidirectionalStreamCommand extends InvokeModelWithBidirectionalStreamCommand_base {
  protected static __types: {
    api: {
      input: InvokeModelWithBidirectionalStreamRequest;
      output: InvokeModelWithBidirectionalStreamResponse;
    };
    sdk: {
      input: InvokeModelWithBidirectionalStreamCommandInput;
      output: InvokeModelWithBidirectionalStreamCommandOutput;
    };
  };
}
