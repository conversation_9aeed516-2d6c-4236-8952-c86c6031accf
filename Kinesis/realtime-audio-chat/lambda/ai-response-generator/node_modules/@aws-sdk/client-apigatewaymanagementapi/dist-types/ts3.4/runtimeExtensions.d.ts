import { ApiGatewayManagementApiExtensionConfiguration } from "./extensionConfiguration";
export interface RuntimeExtension {
  configure(
    extensionConfiguration: ApiGatewayManagementApiExtensionConfiguration
  ): void;
}
export interface RuntimeExtensionsConfig {
  extensions: RuntimeExtension[];
}
export declare const resolveRuntimeExtensions: (
  runtimeConfig: any,
  extensions: RuntimeExtension[]
) => any;
