import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import {
  BatchExecuteStatementCommandInput,
  BatchExecuteStatementCommandOutput,
} from "../commands/BatchExecuteStatementCommand";
import {
  BatchGetItemCommandInput,
  BatchGetItemCommandOutput,
} from "../commands/BatchGetItemCommand";
import {
  BatchWriteItemCommandInput,
  BatchWriteItemCommandOutput,
} from "../commands/BatchWriteItemCommand";
import {
  CreateBackupCommandInput,
  CreateBackupCommandOutput,
} from "../commands/CreateBackupCommand";
import {
  CreateGlobalTableCommandInput,
  CreateGlobalTableCommandOutput,
} from "../commands/CreateGlobalTableCommand";
import {
  CreateTableCommandInput,
  CreateTableCommandOutput,
} from "../commands/CreateTableCommand";
import {
  DeleteBackupCommandInput,
  DeleteBackupCommandOutput,
} from "../commands/DeleteBackupCommand";
import {
  DeleteItemCommandInput,
  DeleteItemCommandOutput,
} from "../commands/DeleteItemCommand";
import {
  DeleteResourcePolicyCommandInput,
  DeleteResourcePolicyCommandOutput,
} from "../commands/DeleteResourcePolicyCommand";
import {
  DeleteTableCommandInput,
  DeleteTableCommandOutput,
} from "../commands/DeleteTableCommand";
import {
  DescribeBackupCommandInput,
  DescribeBackupCommandOutput,
} from "../commands/DescribeBackupCommand";
import {
  DescribeContinuousBackupsCommandInput,
  DescribeContinuousBackupsCommandOutput,
} from "../commands/DescribeContinuousBackupsCommand";
import {
  DescribeContributorInsightsCommandInput,
  DescribeContributorInsightsCommandOutput,
} from "../commands/DescribeContributorInsightsCommand";
import {
  DescribeEndpointsCommandInput,
  DescribeEndpointsCommandOutput,
} from "../commands/DescribeEndpointsCommand";
import {
  DescribeExportCommandInput,
  DescribeExportCommandOutput,
} from "../commands/DescribeExportCommand";
import {
  DescribeGlobalTableCommandInput,
  DescribeGlobalTableCommandOutput,
} from "../commands/DescribeGlobalTableCommand";
import {
  DescribeGlobalTableSettingsCommandInput,
  DescribeGlobalTableSettingsCommandOutput,
} from "../commands/DescribeGlobalTableSettingsCommand";
import {
  DescribeImportCommandInput,
  DescribeImportCommandOutput,
} from "../commands/DescribeImportCommand";
import {
  DescribeKinesisStreamingDestinationCommandInput,
  DescribeKinesisStreamingDestinationCommandOutput,
} from "../commands/DescribeKinesisStreamingDestinationCommand";
import {
  DescribeLimitsCommandInput,
  DescribeLimitsCommandOutput,
} from "../commands/DescribeLimitsCommand";
import {
  DescribeTableCommandInput,
  DescribeTableCommandOutput,
} from "../commands/DescribeTableCommand";
import {
  DescribeTableReplicaAutoScalingCommandInput,
  DescribeTableReplicaAutoScalingCommandOutput,
} from "../commands/DescribeTableReplicaAutoScalingCommand";
import {
  DescribeTimeToLiveCommandInput,
  DescribeTimeToLiveCommandOutput,
} from "../commands/DescribeTimeToLiveCommand";
import {
  DisableKinesisStreamingDestinationCommandInput,
  DisableKinesisStreamingDestinationCommandOutput,
} from "../commands/DisableKinesisStreamingDestinationCommand";
import {
  EnableKinesisStreamingDestinationCommandInput,
  EnableKinesisStreamingDestinationCommandOutput,
} from "../commands/EnableKinesisStreamingDestinationCommand";
import {
  ExecuteStatementCommandInput,
  ExecuteStatementCommandOutput,
} from "../commands/ExecuteStatementCommand";
import {
  ExecuteTransactionCommandInput,
  ExecuteTransactionCommandOutput,
} from "../commands/ExecuteTransactionCommand";
import {
  ExportTableToPointInTimeCommandInput,
  ExportTableToPointInTimeCommandOutput,
} from "../commands/ExportTableToPointInTimeCommand";
import {
  GetItemCommandInput,
  GetItemCommandOutput,
} from "../commands/GetItemCommand";
import {
  GetResourcePolicyCommandInput,
  GetResourcePolicyCommandOutput,
} from "../commands/GetResourcePolicyCommand";
import {
  ImportTableCommandInput,
  ImportTableCommandOutput,
} from "../commands/ImportTableCommand";
import {
  ListBackupsCommandInput,
  ListBackupsCommandOutput,
} from "../commands/ListBackupsCommand";
import {
  ListContributorInsightsCommandInput,
  ListContributorInsightsCommandOutput,
} from "../commands/ListContributorInsightsCommand";
import {
  ListExportsCommandInput,
  ListExportsCommandOutput,
} from "../commands/ListExportsCommand";
import {
  ListGlobalTablesCommandInput,
  ListGlobalTablesCommandOutput,
} from "../commands/ListGlobalTablesCommand";
import {
  ListImportsCommandInput,
  ListImportsCommandOutput,
} from "../commands/ListImportsCommand";
import {
  ListTablesCommandInput,
  ListTablesCommandOutput,
} from "../commands/ListTablesCommand";
import {
  ListTagsOfResourceCommandInput,
  ListTagsOfResourceCommandOutput,
} from "../commands/ListTagsOfResourceCommand";
import {
  PutItemCommandInput,
  PutItemCommandOutput,
} from "../commands/PutItemCommand";
import {
  PutResourcePolicyCommandInput,
  PutResourcePolicyCommandOutput,
} from "../commands/PutResourcePolicyCommand";
import {
  QueryCommandInput,
  QueryCommandOutput,
} from "../commands/QueryCommand";
import {
  RestoreTableFromBackupCommandInput,
  RestoreTableFromBackupCommandOutput,
} from "../commands/RestoreTableFromBackupCommand";
import {
  RestoreTableToPointInTimeCommandInput,
  RestoreTableToPointInTimeCommandOutput,
} from "../commands/RestoreTableToPointInTimeCommand";
import { ScanCommandInput, ScanCommandOutput } from "../commands/ScanCommand";
import {
  TagResourceCommandInput,
  TagResourceCommandOutput,
} from "../commands/TagResourceCommand";
import {
  TransactGetItemsCommandInput,
  TransactGetItemsCommandOutput,
} from "../commands/TransactGetItemsCommand";
import {
  TransactWriteItemsCommandInput,
  TransactWriteItemsCommandOutput,
} from "../commands/TransactWriteItemsCommand";
import {
  UntagResourceCommandInput,
  UntagResourceCommandOutput,
} from "../commands/UntagResourceCommand";
import {
  UpdateContinuousBackupsCommandInput,
  UpdateContinuousBackupsCommandOutput,
} from "../commands/UpdateContinuousBackupsCommand";
import {
  UpdateContributorInsightsCommandInput,
  UpdateContributorInsightsCommandOutput,
} from "../commands/UpdateContributorInsightsCommand";
import {
  UpdateGlobalTableCommandInput,
  UpdateGlobalTableCommandOutput,
} from "../commands/UpdateGlobalTableCommand";
import {
  UpdateGlobalTableSettingsCommandInput,
  UpdateGlobalTableSettingsCommandOutput,
} from "../commands/UpdateGlobalTableSettingsCommand";
import {
  UpdateItemCommandInput,
  UpdateItemCommandOutput,
} from "../commands/UpdateItemCommand";
import {
  UpdateKinesisStreamingDestinationCommandInput,
  UpdateKinesisStreamingDestinationCommandOutput,
} from "../commands/UpdateKinesisStreamingDestinationCommand";
import {
  UpdateTableCommandInput,
  UpdateTableCommandOutput,
} from "../commands/UpdateTableCommand";
import {
  UpdateTableReplicaAutoScalingCommandInput,
  UpdateTableReplicaAutoScalingCommandOutput,
} from "../commands/UpdateTableReplicaAutoScalingCommand";
import {
  UpdateTimeToLiveCommandInput,
  UpdateTimeToLiveCommandOutput,
} from "../commands/UpdateTimeToLiveCommand";
export declare const se_BatchExecuteStatementCommand: (
  input: BatchExecuteStatementCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_BatchGetItemCommand: (
  input: BatchGetItemCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_BatchWriteItemCommand: (
  input: BatchWriteItemCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateBackupCommand: (
  input: CreateBackupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateGlobalTableCommand: (
  input: CreateGlobalTableCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateTableCommand: (
  input: CreateTableCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBackupCommand: (
  input: DeleteBackupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteItemCommand: (
  input: DeleteItemCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteResourcePolicyCommand: (
  input: DeleteResourcePolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteTableCommand: (
  input: DeleteTableCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeBackupCommand: (
  input: DescribeBackupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeContinuousBackupsCommand: (
  input: DescribeContinuousBackupsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeContributorInsightsCommand: (
  input: DescribeContributorInsightsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeEndpointsCommand: (
  input: DescribeEndpointsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeExportCommand: (
  input: DescribeExportCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeGlobalTableCommand: (
  input: DescribeGlobalTableCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeGlobalTableSettingsCommand: (
  input: DescribeGlobalTableSettingsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeImportCommand: (
  input: DescribeImportCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeKinesisStreamingDestinationCommand: (
  input: DescribeKinesisStreamingDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeLimitsCommand: (
  input: DescribeLimitsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeTableCommand: (
  input: DescribeTableCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeTableReplicaAutoScalingCommand: (
  input: DescribeTableReplicaAutoScalingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeTimeToLiveCommand: (
  input: DescribeTimeToLiveCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DisableKinesisStreamingDestinationCommand: (
  input: DisableKinesisStreamingDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_EnableKinesisStreamingDestinationCommand: (
  input: EnableKinesisStreamingDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ExecuteStatementCommand: (
  input: ExecuteStatementCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ExecuteTransactionCommand: (
  input: ExecuteTransactionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ExportTableToPointInTimeCommand: (
  input: ExportTableToPointInTimeCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetItemCommand: (
  input: GetItemCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetResourcePolicyCommand: (
  input: GetResourcePolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ImportTableCommand: (
  input: ImportTableCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListBackupsCommand: (
  input: ListBackupsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListContributorInsightsCommand: (
  input: ListContributorInsightsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListExportsCommand: (
  input: ListExportsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListGlobalTablesCommand: (
  input: ListGlobalTablesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListImportsCommand: (
  input: ListImportsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTablesCommand: (
  input: ListTablesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTagsOfResourceCommand: (
  input: ListTagsOfResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutItemCommand: (
  input: PutItemCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutResourcePolicyCommand: (
  input: PutResourcePolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_QueryCommand: (
  input: QueryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_RestoreTableFromBackupCommand: (
  input: RestoreTableFromBackupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_RestoreTableToPointInTimeCommand: (
  input: RestoreTableToPointInTimeCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ScanCommand: (
  input: ScanCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_TagResourceCommand: (
  input: TagResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_TransactGetItemsCommand: (
  input: TransactGetItemsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_TransactWriteItemsCommand: (
  input: TransactWriteItemsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UntagResourceCommand: (
  input: UntagResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateContinuousBackupsCommand: (
  input: UpdateContinuousBackupsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateContributorInsightsCommand: (
  input: UpdateContributorInsightsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateGlobalTableCommand: (
  input: UpdateGlobalTableCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateGlobalTableSettingsCommand: (
  input: UpdateGlobalTableSettingsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateItemCommand: (
  input: UpdateItemCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateKinesisStreamingDestinationCommand: (
  input: UpdateKinesisStreamingDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateTableCommand: (
  input: UpdateTableCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateTableReplicaAutoScalingCommand: (
  input: UpdateTableReplicaAutoScalingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateTimeToLiveCommand: (
  input: UpdateTimeToLiveCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_BatchExecuteStatementCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<BatchExecuteStatementCommandOutput>;
export declare const de_BatchGetItemCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<BatchGetItemCommandOutput>;
export declare const de_BatchWriteItemCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<BatchWriteItemCommandOutput>;
export declare const de_CreateBackupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateBackupCommandOutput>;
export declare const de_CreateGlobalTableCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateGlobalTableCommandOutput>;
export declare const de_CreateTableCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateTableCommandOutput>;
export declare const de_DeleteBackupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBackupCommandOutput>;
export declare const de_DeleteItemCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteItemCommandOutput>;
export declare const de_DeleteResourcePolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteResourcePolicyCommandOutput>;
export declare const de_DeleteTableCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteTableCommandOutput>;
export declare const de_DescribeBackupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeBackupCommandOutput>;
export declare const de_DescribeContinuousBackupsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeContinuousBackupsCommandOutput>;
export declare const de_DescribeContributorInsightsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeContributorInsightsCommandOutput>;
export declare const de_DescribeEndpointsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeEndpointsCommandOutput>;
export declare const de_DescribeExportCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeExportCommandOutput>;
export declare const de_DescribeGlobalTableCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeGlobalTableCommandOutput>;
export declare const de_DescribeGlobalTableSettingsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeGlobalTableSettingsCommandOutput>;
export declare const de_DescribeImportCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeImportCommandOutput>;
export declare const de_DescribeKinesisStreamingDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeKinesisStreamingDestinationCommandOutput>;
export declare const de_DescribeLimitsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeLimitsCommandOutput>;
export declare const de_DescribeTableCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeTableCommandOutput>;
export declare const de_DescribeTableReplicaAutoScalingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeTableReplicaAutoScalingCommandOutput>;
export declare const de_DescribeTimeToLiveCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeTimeToLiveCommandOutput>;
export declare const de_DisableKinesisStreamingDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DisableKinesisStreamingDestinationCommandOutput>;
export declare const de_EnableKinesisStreamingDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<EnableKinesisStreamingDestinationCommandOutput>;
export declare const de_ExecuteStatementCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ExecuteStatementCommandOutput>;
export declare const de_ExecuteTransactionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ExecuteTransactionCommandOutput>;
export declare const de_ExportTableToPointInTimeCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ExportTableToPointInTimeCommandOutput>;
export declare const de_GetItemCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetItemCommandOutput>;
export declare const de_GetResourcePolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetResourcePolicyCommandOutput>;
export declare const de_ImportTableCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ImportTableCommandOutput>;
export declare const de_ListBackupsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListBackupsCommandOutput>;
export declare const de_ListContributorInsightsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListContributorInsightsCommandOutput>;
export declare const de_ListExportsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListExportsCommandOutput>;
export declare const de_ListGlobalTablesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListGlobalTablesCommandOutput>;
export declare const de_ListImportsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListImportsCommandOutput>;
export declare const de_ListTablesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTablesCommandOutput>;
export declare const de_ListTagsOfResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTagsOfResourceCommandOutput>;
export declare const de_PutItemCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutItemCommandOutput>;
export declare const de_PutResourcePolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutResourcePolicyCommandOutput>;
export declare const de_QueryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<QueryCommandOutput>;
export declare const de_RestoreTableFromBackupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<RestoreTableFromBackupCommandOutput>;
export declare const de_RestoreTableToPointInTimeCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<RestoreTableToPointInTimeCommandOutput>;
export declare const de_ScanCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ScanCommandOutput>;
export declare const de_TagResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<TagResourceCommandOutput>;
export declare const de_TransactGetItemsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<TransactGetItemsCommandOutput>;
export declare const de_TransactWriteItemsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<TransactWriteItemsCommandOutput>;
export declare const de_UntagResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UntagResourceCommandOutput>;
export declare const de_UpdateContinuousBackupsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateContinuousBackupsCommandOutput>;
export declare const de_UpdateContributorInsightsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateContributorInsightsCommandOutput>;
export declare const de_UpdateGlobalTableCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateGlobalTableCommandOutput>;
export declare const de_UpdateGlobalTableSettingsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateGlobalTableSettingsCommandOutput>;
export declare const de_UpdateItemCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateItemCommandOutput>;
export declare const de_UpdateKinesisStreamingDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateKinesisStreamingDestinationCommandOutput>;
export declare const de_UpdateTableCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateTableCommandOutput>;
export declare const de_UpdateTableReplicaAutoScalingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateTableReplicaAutoScalingCommandOutput>;
export declare const de_UpdateTimeToLiveCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateTimeToLiveCommandOutput>;
