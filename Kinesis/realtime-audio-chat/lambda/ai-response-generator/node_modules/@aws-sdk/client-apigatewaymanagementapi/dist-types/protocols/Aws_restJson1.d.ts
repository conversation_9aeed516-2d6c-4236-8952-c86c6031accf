import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import { DeleteConnectionCommandInput, DeleteConnectionCommandOutput } from "../commands/DeleteConnectionCommand";
import { GetConnectionCommandInput, GetConnectionCommandOutput } from "../commands/GetConnectionCommand";
import { PostToConnectionCommandInput, PostToConnectionCommandOutput } from "../commands/PostToConnectionCommand";
/**
 * serializeAws_restJson1DeleteConnectionCommand
 */
export declare const se_DeleteConnectionCommand: (input: DeleteConnectionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetConnectionCommand
 */
export declare const se_GetConnectionCommand: (input: GetConnectionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PostToConnectionCommand
 */
export declare const se_PostToConnectionCommand: (input: PostToConnectionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_restJson1DeleteConnectionCommand
 */
export declare const de_DeleteConnectionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteConnectionCommandOutput>;
/**
 * deserializeAws_restJson1GetConnectionCommand
 */
export declare const de_GetConnectionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetConnectionCommandOutput>;
/**
 * deserializeAws_restJson1PostToConnectionCommand
 */
export declare const de_PostToConnectionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PostToConnectionCommandOutput>;
