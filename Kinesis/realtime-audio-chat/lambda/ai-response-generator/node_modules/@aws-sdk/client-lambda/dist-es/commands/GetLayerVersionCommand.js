import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_GetLayerVersionCommand, se_GetLayerVersionCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class GetLayerVersionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AWSGirApiService", "GetLayerVersion", {})
    .n("LambdaClient", "GetLayerVersionCommand")
    .f(void 0, void 0)
    .ser(se_GetLayerVersionCommand)
    .de(de_GetLayerVersionCommand)
    .build() {
}
