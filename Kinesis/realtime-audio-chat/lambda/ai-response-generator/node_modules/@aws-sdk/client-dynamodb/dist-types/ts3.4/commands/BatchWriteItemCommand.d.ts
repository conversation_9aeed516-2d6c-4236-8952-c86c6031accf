import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DynamoDBClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../DynamoDBClient";
import { BatchWriteItemInput, BatchWriteItemOutput } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface BatchWriteItemCommandInput extends BatchWriteItemInput {}
export interface BatchWriteItemCommandOutput
  extends BatchWriteItemOutput,
    __MetadataBearer {}
declare const BatchWriteItemCommand_base: {
  new (
    input: BatchWriteItemCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchWriteItemCommandInput,
    BatchWriteItemCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: BatchWriteItemCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchWriteItemCommandInput,
    BatchWriteItemCommandOutput,
    DynamoDBClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class BatchWriteItemCommand extends BatchWriteItemCommand_base {
  protected static __types: {
    api: {
      input: BatchWriteItemInput;
      output: BatchWriteItemOutput;
    };
    sdk: {
      input: BatchWriteItemCommandInput;
      output: BatchWriteItemCommandOutput;
    };
  };
}
