import { Command as $Command } from "@smithy/smithy-client";
import {
  BlobPayloadInputTypes,
  MetadataBearer as __MetadataBearer,
} from "@smithy/types";
import {
  ApiGatewayManagementApiClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ApiGatewayManagementApiClient";
import { PostToConnectionRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export type PostToConnectionCommandInputType = Pick<
  PostToConnectionRequest,
  Exclude<keyof PostToConnectionRequest, "Data">
> & {
  Data: BlobPayloadInputTypes;
};
export interface PostToConnectionCommandInput
  extends PostToConnectionCommandInputType {}
export interface PostToConnectionCommandOutput extends __MetadataBearer {}
declare const PostToConnectionCommand_base: {
  new (
    input: PostToConnectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PostToConnectionCommandInput,
    PostToConnectionCommandOutput,
    ApiGatewayManagementApiClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PostToConnectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PostToConnectionCommandInput,
    PostToConnectionCommandOutput,
    ApiGatewayManagementApiClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PostToConnectionCommand extends PostToConnectionCommand_base {
  protected static __types: {
    api: {
      input: PostToConnectionRequest;
      output: {};
    };
    sdk: {
      input: PostToConnectionCommandInput;
      output: PostToConnectionCommandOutput;
    };
  };
}
